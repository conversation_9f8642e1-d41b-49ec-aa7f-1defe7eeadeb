<script setup lang="ts">
import type { PropType } from 'vue';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { CompanyBankBO, CompanyInfo, CompanyInvoiceBO, CompanyMortgageBO, CompanyShareholderBO } from '#/api';

import { computed, ref } from 'vue';

import { COL_SPAN_PROP, FORM_PROP } from '@vben/constants';
import { BasicCaption, FeUserSelect } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';

import { SyncOutlined } from '@ant-design/icons-vue';
import { Modal as AntdModal, Input, message, TreeSelect } from 'ant-design-vue';
import dayjs from 'dayjs';

import { BaseAttachmentList, BaseFilePickList, BaseRegionPicker } from '#/adapter/base-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getIndustryList<PERSON>pi, getUserInfoByIdsApi, getUserList<PERSON>yKeywordApi, getUserTreeListApi } from '#/api';

const props = defineProps({
  syncMainInfo: { type: Function as PropType<(keyword?: string) => void>, required: true },
  syncShareholderInfo: { type: Function as PropType<(keyword?: string) => void>, required: true },
  syncMortgageInfo: { type: Function as PropType<(keyword?: string) => void>, required: true },
});
const dictStore = useDictStore();
const companyForm = defineModel<CompanyInfo>({ type: Object, required: true });
const rules = {
  companyName: [{ required: true, message: '请输入企业名称' }],
  companyCode: [{ required: true, message: '请输入统一社会信用代码' }],
  legalPersonName: [{ required: true, message: '请输入法人' }],
};
const colSpan = COL_SPAN_PROP;
const formProp = FORM_PROP;
const businessTerm = computed({
  get() {
    return [
      companyForm.value.fromTime ? Number(dayjs(companyForm.value.fromTime).format('x')) : undefined,
      companyForm.value.toTime ? Number(dayjs(companyForm.value.toTime).format('x')) : undefined,
    ];
  },
  set(value) {
    companyForm.value.fromTime = value[0] ? Number(value[0]) : undefined;
    companyForm.value.toTime = value[1] ? Number(value[1]) : undefined;
  },
});
const establishTime = computed({
  get() {
    return companyForm.value.establishTime ? dayjs(companyForm.value.establishTime).format('x') : undefined;
  },
  set(value) {
    companyForm.value.establishTime = value ? Number(value) : undefined;
  },
});
const init = (data: CompanyInfo) => {
  bankGridApi.grid.reloadData(data.companyBankList ?? []);
  invoiceGridApi.grid.reloadData(data.companyInvoiceList ?? []);
  mortgageGridApi.grid.reloadData(data.mortgageList ?? []);
  shareholderGridApi.grid.reloadData(data.shareholderList ?? []);
};
const handleBusinessTermChange = (val?: [Date, Date]) => {
  companyForm.value.fromTime = val ? Number(val[0]) : undefined;
  companyForm.value.toTime = val ? Number(val[1]) : undefined;
};
const baseGridOptions = {
  showOverflow: true,
  keepSource: true,
  rowConfig: {
    drag: true,
  },
  pagerConfig: {
    enabled: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbarTools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const bankGridOptions = {
  data: companyForm.value.companyBankList,
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    {
      field: 'accountName',
      title: '账户名称',
      slots: { default: 'edit_account_name' },
      minWidth: '160px',
    },
    {
      field: 'account',
      title: '银行账号',
      slots: { default: 'edit_account' },
      minWidth: '160px',
    },
    {
      field: 'bank',
      title: '开户银行',
      slots: { default: 'edit_bank' },
      minWidth: '160px',
    },
    {
      field: 'branchNumber',
      title: '开户行号',
      slots: { default: 'edit_branch_number' },
      minWidth: '160px',
    },
    {
      field: 'isDefault',
      title: '是否默认',
      slots: { default: 'edit_is_default' },
      minWidth: '160px',
      fixed: 'right',
    },
  ],
  editRules: {
    accountName: [{ required: true, content: '请输入账户名称' }],
    account: [{ required: true, content: '请输入银行账号' }],
  },
  ...baseGridOptions,
} as VxeTableGridOptions;
const [BankGrid, bankGridApi] = useVbenVxeGrid({
  gridOptions: bankGridOptions,
});
const addBank = async () => {
  const record: CompanyBankBO = {};
  const { visibleData } = bankGridApi.grid.getTableData();
  if (visibleData.length === 0) {
    record.isDefault = 1;
  }
  const $grid = bankGridApi.grid;
  if ($grid) {
    const { row } = await $grid.insertAt(record, -1);
    await $grid.setEditRow(row);
  }
};
const removeBank = async () => {
  const $grid = bankGridApi.grid;
  if ($grid) {
    const selectRecords = $grid.getCheckboxRecords();
    if (selectRecords.length > 0) {
      AntdModal.confirm({
        title: '确认删除',
        content: '确认删除此数据吗？',
        async onOk() {
          $grid.removeCheckboxRow();
          message.success($t('base.resSuccess'));
        },
      });
    } else {
      message.warning('请选择数据');
    }
  }
};
const handleBankIsDefaultChange = (_value: boolean, row: CompanyInvoiceBO) => {
  const { visibleData } = bankGridApi.grid.getTableData();
  visibleData.forEach((item) => {
    item.isDefault = 0;
  });
  row.isDefault = 1;
};

const invoiceGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    { field: 'title', title: '抬头名称', slots: { default: 'edit_title' }, minWidth: '160px' },
    {
      field: 'taxNumber',
      title: '纳税人识别号',
      slots: { default: 'edit_tax_number' },
      minWidth: '160px',
    },
    {
      field: 'bank',
      title: '开户银行',
      slots: { default: 'edit_bank' },
      minWidth: '160px',
    },
    {
      field: 'account',
      title: '开户银行账号',
      slots: { default: 'edit_account' },
      minWidth: '160px',
    },
    {
      field: 'phone',
      title: '电话',
      slots: { default: 'edit_phone' },
      minWidth: '160px',
    },
    {
      field: 'address',
      title: '地址',
      slots: { default: 'edit_address' },
      minWidth: '160px',
    },
    {
      field: 'isDefault',
      title: '是否默认',
      slots: { default: 'edit_is_default' },
      minWidth: '160px',
      fixed: 'right',
    },
  ],
  editRules: {
    title: [{ required: true, content: '请输入抬头名称' }],
    taxNumber: [{ required: true, content: '请输入纳税人识别号' }],
  },
  ...baseGridOptions,
} as VxeTableGridOptions;
const [InvoiceGrid, invoiceGridApi] = useVbenVxeGrid({
  gridOptions: invoiceGridOptions,
});
const addInvoice = async () => {
  const record: CompanyInvoiceBO = {};
  const { visibleData } = invoiceGridApi.grid.getTableData();
  if (visibleData.length === 0) {
    record.isDefault = 1;
  }
  const $grid = invoiceGridApi.grid;
  if ($grid) {
    const { row } = await $grid.insertAt(record, -1);
    await $grid.setEditRow(row);
  }
};
const removeInvoice = async () => {
  const $grid = invoiceGridApi.grid;
  if ($grid) {
    const selectRecords = $grid.getCheckboxRecords();
    if (selectRecords.length > 0) {
      AntdModal.confirm({
        title: '确认删除',
        content: '确认删除此数据吗？',
        async onOk() {
          $grid.removeCheckboxRow();
          message.success($t('base.resSuccess'));
        },
      });
    } else {
      message.warning('请选择数据');
    }
  }
};
const handleInvoiceIsDefaultChange = (_value: boolean, row: CompanyInvoiceBO) => {
  const { visibleData } = invoiceGridApi.grid.getTableData();
  visibleData.forEach((item) => {
    item.isDefault = 0;
  });
  row.isDefault = 1;
};
const shareholderGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    {
      field: 'name',
      title: '股东名称',
      slots: { default: 'edit_name' },
      minWidth: '160px',
    },
    {
      field: 'shouldCapital',
      title: '认缴出资额（万元）',
      slots: { default: 'edit_should_capital' },
      minWidth: '160px',
    },
    {
      field: 'stockPercent',
      title: '股比',
      slots: { default: 'edit_stock_percent' },
      minWidth: '160px',
    },
    {
      field: 'realCapital',
      title: '实缴金额（万元）',
      slots: { default: 'edit_real_capital' },
      minWidth: '160px',
    },
    {
      field: 'startDate',
      title: '首次持股日期',
      slots: { default: 'edit_start_date' },
      minWidth: '160px',
    },
  ],
  editRules: {
    name: [{ required: true, content: '请输入股东名称' }],
  },
  ...baseGridOptions,
} as VxeTableGridOptions;
const [ShareholderGrid, shareholderGridApi] = useVbenVxeGrid({
  gridOptions: shareholderGridOptions,
});
const addShareholder = async () => {
  const record: CompanyShareholderBO = {};
  const $grid = shareholderGridApi.grid;
  if ($grid) {
    const { row } = await $grid.insertAt(record, -1);
    await $grid.setEditRow(row);
  }
};
const removeShareholder = async () => {
  const $grid = shareholderGridApi.grid;
  if ($grid) {
    const selectRecords = $grid.getCheckboxRecords();
    if (selectRecords.length > 0) {
      AntdModal.confirm({
        title: '确认删除',
        content: '确认删除此数据吗？',
        async onOk() {
          $grid.removeCheckboxRow();
          message.success($t('base.resSuccess'));
        },
      });
    } else {
      message.warning('请选择数据');
    }
  }
};
const mortgageGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    {
      field: 'regDate',
      title: '登记时间',
      slots: { default: 'edit_reg_date' },
      minWidth: '160px',
    },
    {
      field: 'number',
      title: '登记编号',
      slots: { default: 'edit_number' },
      minWidth: '160px',
    },
    {
      field: 'mortgagee',
      title: '抵押权人',
      slots: { default: 'edit_mortgagee' },
      minWidth: '160px',
    },
    {
      field: 'debitType',
      title: '债务类型',
      slots: { default: 'edit_debit_type' },
      minWidth: '160px',
    },
    {
      field: 'amount',
      title: '被担保债权数额',
      slots: { default: 'edit_amount' },
      minWidth: '160px',
    },
    {
      field: 'department',
      title: '登记机关',
      slots: { default: 'edit_department' },
      minWidth: '160px',
    },
    {
      field: 'startDate',
      title: '开始日期',
      slots: { default: 'edit_start_date' },
      minWidth: '160px',
    },
    {
      field: 'endDate',
      title: '结束日期',
      slots: { default: 'edit_end_date' },
      minWidth: '160px',
    },
  ],
  editRules: {
    regDate: [{ required: true, content: '请输入登记时间' }],
    number: [{ required: true, content: '请输入登记编号' }],
  },
  ...baseGridOptions,
} as VxeTableGridOptions;
const [MortgageGrid, mortgageGridApi] = useVbenVxeGrid({
  gridOptions: mortgageGridOptions,
});
const addMortgage = async () => {
  const record: CompanyMortgageBO = {};
  const $grid = mortgageGridApi.grid;
  if ($grid) {
    const { row } = await $grid.insertAt(record, -1);
    await $grid.setEditRow(row);
  }
};
const removeMortgage = async () => {
  const $grid = mortgageGridApi.grid;
  if ($grid) {
    const selectRecords = $grid.getCheckboxRecords();
    if (selectRecords.length > 0) {
      AntdModal.confirm({
        title: '确认删除',
        content: '确认删除此数据吗？',
        async onOk() {
          $grid.removeCheckboxRow();
          message.success($t('base.resSuccess'));
        },
      });
    } else {
      message.warning('请选择数据');
    }
  }
};
const syncMainInfo = async () => {
  await props.syncMainInfo(companyForm.value.companyCode);
};
const syncShareholderInfo = async () => {
  await props.syncShareholderInfo(companyForm.value.companyCode);
  await shareholderGridApi.grid.reloadData(companyForm.value.shareholderList ?? []);
};
const syncMortgageInfo = async () => {
  await props.syncMortgageInfo(companyForm.value.companyCode);
  await mortgageGridApi.grid.reloadData(companyForm.value.mortgageList ?? []);
};
const CompanyFormRef = ref();
const save = async () => {
  await CompanyFormRef.value.validate();
  const bankErrMap = await bankGridApi.grid.validate(true);
  if (bankErrMap) {
    const bankErrMessage = getCombinedErrorMessagesString(bankErrMap);
    if (bankErrMessage) {
      message.error(bankErrMessage);
      throw new Error(bankErrMessage);
    }
  }
  const { visibleData: bankData } = bankGridApi.grid.getTableData();
  companyForm.value.companyBankList = bankData;
  const invoiceErrMap = await invoiceGridApi.grid.validate(true);
  if (invoiceErrMap) {
    const invoiceErrMessage = getCombinedErrorMessagesString(invoiceErrMap);
    if (invoiceErrMessage) {
      message.error(invoiceErrMessage);
      throw new Error(invoiceErrMessage);
    }
  }
  const { visibleData: invoiceData } = invoiceGridApi.grid.getTableData();
  companyForm.value.companyInvoiceList = invoiceData;
  const shareholderErrMap = await shareholderGridApi.grid.validate(true);
  if (shareholderErrMap) {
    const shareholderErrMessage = getCombinedErrorMessagesString(shareholderErrMap);
    if (shareholderErrMessage) {
      message.error(shareholderErrMessage);
      throw new Error(shareholderErrMessage);
    }
  }
  const { visibleData: shareholderData } = shareholderGridApi.grid.getTableData();
  shareholderData.forEach((item) => {
    if (item.startDate) {
      item.startDate = Number(item.startDate);
    }
  });
  companyForm.value.shareholderList = shareholderData;
  const mortgageErrMap = await mortgageGridApi.grid.validate(true);
  if (mortgageErrMap) {
    const mortgageErrMessage = getCombinedErrorMessagesString(mortgageErrMap);
    if (mortgageErrMessage) {
      message.error(mortgageErrMessage);
      throw new Error(mortgageErrMessage);
    }
  }
  const { visibleData: mortgageData } = mortgageGridApi.grid.getTableData();
  companyForm.value.mortgageList = mortgageData;
  return companyForm.value;
  // console.error(bankErrMap, invoiceErrMap);
};
const industryOptions = ref([]);
const getIndustryList = async () => {
  industryOptions.value = await getIndustryListApi();
};
getIndustryList();
/**
 * 提取所有字段的错误信息，并格式化成一个单一的字符串。
 */
function getCombinedErrorMessagesString(
  errorObject: Record<string, any[]>,
  separator = '；',
  prefix = '以下字段存在问题：',
  defaultMessage = '未知错误，请重试。',
) {
  // 1. 确保 errorObject 存在且是对象
  if (!errorObject || typeof errorObject !== 'object' || Object.keys(errorObject).length === 0) {
    return defaultMessage;
  }

  const messages = [];

  // 2. 遍历 errorObject 的所有键（字段名）
  const fieldKeys = Object.keys(errorObject);

  for (const key of fieldKeys) {
    // 3. 使用可选链操作符安全地访问嵌套的 message
    // errorObject[key] 对应 accountName 或 phoneNumber 数组
    // [0] 取数组的第一个元素 (通常错误对象数组只有一个元素)
    // .rule 访问 rule 对象
    // .message 访问最终的错误消息
    const message = errorObject[key]?.[0]?.rule?.message;

    // 4. 如果找到了有效的 message，就添加到 messages 数组中
    if (message) {
      messages.push(message);
    }
  }

  // 5. 判断是否收集到任何错误信息
  return messages.length > 0 ? prefix + messages.join(separator) : defaultMessage;
}
defineExpose({ init, save });
</script>

<template>
  <a-form ref="CompanyFormRef" class="" :model="companyForm" :rules="rules" v-bind="formProp">
    <BasicCaption content="工商信息">
      <template #action>
        <a-typography-link title="更新工商信息" @click="syncMainInfo()">
          <SyncOutlined />
        </a-typography-link>
      </template>
    </BasicCaption>
    <a-row class="mt-5">
      <a-col v-bind="colSpan">
        <a-form-item label="企业名称" name="companyName">
          <a-input v-model:value="companyForm.companyName" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="统一社会信用代码" name="companyCode">
          <a-input v-model:value="companyForm.companyCode" :disabled="!!companyForm.id" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="注册资本" name="regCapital">
          <a-input v-model:value="companyForm.regCapital" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="实缴资本" name="actualCapital">
          <a-input v-model:value="companyForm.actualCapital" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="成立日期" name="establishTime">
          <a-date-picker v-model:value="establishTime" value-format="x" class="w-full" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="经营状态" name="regStatus">
          <a-select v-model:value="companyForm.regStatus" :options="dictStore.getDictList('COMPANY_REG_STATUS')" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="工商注册号" name="regNumber">
          <a-input v-model:value="companyForm.regNumber" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="组织机构代码" name="orgNumber">
          <a-input v-model:value="companyForm.orgNumber" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="公司类型" name="companyOrgType">
          <a-select v-model:value="companyForm.companyOrgType" :options="dictStore.getDictList('COMPANY_ORG_TYPE')" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="营业期限" name="fromTime">
          <a-range-picker
            :value="businessTerm"
            class="w-full"
            value-format="x"
            @calendar-change="handleBusinessTermChange"
            @change="handleBusinessTermChange"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="所属行业" name="industry">
          <TreeSelect
            v-model:value="companyForm.industry"
            :tree-data="industryOptions"
            :field-names="{ label: 'name', value: 'code' }"
            :tree-default-expanded-keys="companyForm.industry ? [companyForm.industry] : []"
            tree-node-filter-prop="name"
            checkable
            show-search
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="行政区划" name="district">
          <BaseRegionPicker
            v-model:province="companyForm.province"
            v-model:city="companyForm.city"
            v-model:district="companyForm.district"
            v-model:district-code="companyForm.districtCode"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="注册地址" name="regLocation">
          <a-input v-model:value="companyForm.regLocation" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="经营地址" name="bizLocation">
          <a-input v-model:value="companyForm.bizLocation" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="经营范围" name="bizScope" :label-col="{ span: 2 }" :wrapper-col="{ span: 22 }">
          <a-textarea v-model:value="companyForm.bizScope" :rows="4" class="w-full" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="营业执照" name="" :label-col="{ span: 2 }" :wrapper-col="{ span: 22 }">
          <BaseFilePickList list-type="picture-card" filter-file-type="imageType" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="其他备注" name="remark" :label-col="{ span: 2 }" :wrapper-col="{ span: 22 }">
          <a-textarea v-model:value="companyForm.remark" :rows="4" class="w-full" />
        </a-form-item>
      </a-col>
    </a-row>
    <BasicCaption content="法人代表信息" />
    <a-row class="mt-5">
      <a-col v-bind="colSpan">
        <a-form-item label="法定代表人" name="legalPersonName">
          <a-input v-model:value="companyForm.legalPersonName" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="证件号码" name="legalPersonIdNo">
          <a-input v-model:value="companyForm.legalPersonIdNo" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="联系电话" name="legalPersonPhone">
          <a-input v-model:value="companyForm.legalPersonPhone" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan" />
      <a-col v-bind="colSpan">
        <a-form-item label="法人证件正面" name="legalPersonIdCardFront">
          <BaseFilePickList
            v-model="companyForm.legalPersonIdCardFront"
            list-type="picture-card"
            filter-file-type="imageType"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="法人证件背面" name="legalPersonIdCardBack">
          <BaseFilePickList
            v-model="companyForm.legalPersonIdCardBack"
            list-type="picture-card"
            filter-file-type="imageType"
          />
        </a-form-item>
      </a-col>
    </a-row>
    <BasicCaption content="企业联系人信息" />
    <a-row class="mt-5">
      <a-col v-bind="colSpan">
        <a-form-item label="联系人姓名" name="contactPersonName">
          <a-input v-model:value="companyForm.contactPersonName" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="联系人电话" name="contactPersonPhone">
          <a-input v-model:value="companyForm.contactPersonPhone" />
        </a-form-item>
      </a-col>
    </a-row>
    <BasicCaption content="客户负责人" />
    <a-row class="mt-5">
      <a-col v-bind="colSpan">
        <a-form-item label="业务经理" name="">
          <FeUserSelect
            v-model:value="companyForm.managerList"
            multiple
            :api="{ getUserInfoByIdsApi, getUserListByKeywordApi, getUserTreeListApi }"
          />
        </a-form-item>
      </a-col>
    </a-row>
    <BasicCaption content="银行账户" />
    <BankGrid>
      <template #toolbarTools>
        <a-space>
          <a-button type="primary" @click="addBank">增行</a-button>
          <a-button danger @click="removeBank">删行</a-button>
        </a-space>
      </template>
      <template #edit_account_name="{ row }">
        <Input v-model:value="row.accountName" placeholder="请输入账户名称" />
      </template>
      <template #edit_account="{ row }">
        <Input v-model:value="row.account" placeholder="请输入银行账号" />
      </template>
      <template #edit_bank="{ row }">
        <Input v-model:value="row.bank" placeholder="请输入开户银行" />
      </template>
      <template #edit_branch_number="{ row }">
        <Input v-model:value="row.branchNumber" placeholder="请输入开户行号" />
      </template>
      <template #edit_is_default="{ row }">
        <a-checkable-tag :checked="!!row.isDefault" @change="(value: boolean) => handleBankIsDefaultChange(value, row)">
          默认
        </a-checkable-tag>
      </template>
    </BankGrid>
    <BasicCaption content="开票信息" />
    <InvoiceGrid>
      <template #toolbarTools>
        <a-space>
          <a-button type="primary" @click="addInvoice">增行</a-button>
          <a-button danger @click="removeInvoice">删行</a-button>
        </a-space>
      </template>
      <template #edit_title="{ row }">
        <Input v-model:value="row.title" placeholder="请输入抬头名称" />
      </template>
      <template #edit_tax_number="{ row }">
        <Input v-model:value="row.taxNumber" placeholder="请输入纳税人识别号" />
      </template>
      <template #edit_bank="{ row }">
        <Input v-model:value="row.bank" placeholder="请输入开户银行" />
      </template>
      <template #edit_account="{ row }">
        <Input v-model:value="row.account" placeholder="请输入开户银行账号" />
      </template>
      <template #edit_phone="{ row }">
        <Input v-model:value="row.phone" placeholder="请输入电话" />
      </template>
      <template #edit_address="{ row }">
        <Input v-model:value="row.address" placeholder="请输入开户地址" />
      </template>
      <template #edit_is_default="{ row }">
        <a-checkable-tag
          :checked="!!row.isDefault"
          @change="(value: boolean) => handleInvoiceIsDefaultChange(value, row)"
        >
          默认
        </a-checkable-tag>
      </template>
    </InvoiceGrid>
    <BasicCaption content="企业股东">
      <template #action>
        <a-typography-link title="更新企业股东" @click="syncShareholderInfo()">
          <SyncOutlined />
        </a-typography-link>
      </template>
    </BasicCaption>
    <ShareholderGrid>
      <template #toolbarTools>
        <a-space>
          <a-button type="primary" @click="addShareholder">增行</a-button>
          <a-button danger @click="removeShareholder">删行</a-button>
        </a-space>
      </template>
      <template #edit_name="{ row }">
        <Input v-model:value="row.name" placeholder="请输入股东名称" />
      </template>
      <template #edit_should_capital="{ row }">
        <Input v-model:value="row.shouldCapital" placeholder="请输入认缴出资额" />
      </template>
      <template #edit_stock_percent="{ row }">
        <Input v-model:value="row.stockPercent" placeholder="请输入股比" />
      </template>
      <template #edit_real_capital="{ row }">
        <Input v-model:value="row.realCapital" placeholder="请输入实缴金额" />
      </template>
      <template #edit_start_date="{ row }">
        <a-date-picker v-model:value="row.startDate" value-format="x" class="w-full" />
      </template>
    </ShareholderGrid>
    <BasicCaption content="动产抵押">
      <template #action>
        <a-typography-link title="更新动产抵押" @click="syncMortgageInfo()">
          <SyncOutlined />
        </a-typography-link>
      </template>
    </BasicCaption>
    <MortgageGrid>
      <template #toolbarTools>
        <a-space>
          <a-button type="primary" @click="addMortgage">增行</a-button>
          <a-button danger @click="removeMortgage">删行</a-button>
        </a-space>
      </template>
      <template #edit_reg_date="{ row }">
        <a-date-picker v-model:value="row.regDate" value-format="x" class="w-full" />
      </template>
      <template #edit_number="{ row }">
        <Input v-model:value="row.number" placeholder="请输入登记编号" />
      </template>
      <template #edit_mortgagee="{ row }">
        <Input v-model:value="row.mortgagee" placeholder="请输入抵押权人" />
      </template>
      <template #edit_debit_type="{ row }">
        <Input v-model:value="row.debitType" placeholder="请输入债务类型" />
      </template>
      <template #edit_amount="{ row }">
        <Input v-model:value="row.amount" placeholder="请输入被担保债权数额" />
      </template>
      <template #edit_department="{ row }">
        <Input v-model:value="row.department" placeholder="请输入登记机关" />
      </template>
      <template #edit_start_date="{ row }">
        <a-date-picker v-model:value="row.startDate" value-format="x" class="w-full" />
      </template>
      <template #edit_end_date="{ row }">
        <a-date-picker v-model:value="row.endDate" value-format="x" class="w-full" />
      </template>
    </MortgageGrid>
    <BaseAttachmentList
      v-model="companyForm.attachmentList"
      :business-id="companyForm.id"
      business-type="COMPANY"
      edit-mode
    />
  </a-form>
</template>

<style scoped></style>
