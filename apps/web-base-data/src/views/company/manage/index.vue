<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { AccessInfo, CompanyInfo } from '#/api';

import { reactive, ref } from 'vue';

import { Page, prompt, useVbenModal } from '@vben/common-ui';
import { DETAIL_GRID_OPTIONS } from '@vben/constants';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { Modal as AntdModal, message, Select } from 'ant-design-vue';
import { debounce } from 'lodash-es';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  addCompanyRoleApi,
  deleteCompanyApi,
  deleteCompanyRoleApi,
  disableCompanyApi,
  enableCompanyApi,
  getBusinessRoleListApi,
  getCompanyPageListApi,
  getCompanyRolePageApi,
  searchCompanyListApi,
} from '#/api';

import CompanyDetail from './company-detail.vue';
import CompanyEdit from './company-edit.vue';

const dictStore = useDictStore();
const state = reactive({
  fetching: false,
});
const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'companyName',
      label: '企业名称',
    },
    {
      component: 'Input',
      fieldName: 'companyCode',
      label: '统一社会信用代码',
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '企业状态',
      componentProps: {
        options: dictStore.getDictList('COMPANY_STATUS'),
      },
    },
  ],
  commonConfig: {
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
  },
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'companyName', title: '企业名称', minWidth: 200 },
    { field: 'companyCode', title: '统一社会信用代码' },
    { field: 'legalPersonName', title: '法定代表人' },
    { field: 'roles', title: '业务角色', slots: { default: 'roles' } },
    {
      field: 'status',
      title: '状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'COMPANY_STATUS',
        },
      },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 220,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues: { spuCode: string; spuName: string; status: string }) => {
        return await getCompanyPageListApi({
          ...formValues,
          current: page.currentPage,
          size: page.pageSize,
        });
      },
    },
  },
  checkboxConfig: {
    showHeader: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents: {
    checkboxChange: ({ checked, row }: { checked: boolean; row: CompanyInfo }) => {
      if (checked) {
        gridApi.grid.setAllCheckboxRow(false);
        gridApi.grid.setCheckboxRow(row, true);
      }
    },
  },
});
const add = () => {
  searchCompanyModalApi.open();
};
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
const edit = (row: CompanyInfo) => {
  openFormPopup(true, row);
};
const changeMaterialStatus = (status: 0 | 1) => {
  const res = gridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  const id = res[0].id;
  const operation = {
    1: {
      label: '启用',
      api: enableCompanyApi,
    },
    0: {
      label: '禁用',
      api: disableCompanyApi,
    },
  };
  AntdModal.confirm({
    title: `确认${operation[status].label}`,
    content: `确认${operation[status].label}此企业吗？`,
    async onOk() {
      await operation[status].api(id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
const delMaterial = () => {
  const res = gridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  const id = res[0].id;
  AntdModal.confirm({
    title: '确认删除',
    content: '确认删除此企业吗？',
    async onOk() {
      await deleteCompanyApi(id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
const viewDetail = (row: CompanyInfo) => {
  openDetailPopup(true, row);
};
const editSuccess = () => {
  gridApi.reload();
};
const [BusinessRoleGrid, businessRoleGridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: [
      { type: 'checkbox', width: '60px' },
      { field: 'name', title: '业务角色' },
      {
        field: 'status',
        title: '状态',
        cellRender: {
          name: 'CellStatus',
          props: {
            code: 'COMPANY_ROLE_STATUS',
          },
        },
      },
      { field: 'operationTime', title: '操作时间', formatter: 'formatDate' },
    ],
    proxyConfig: {
      autoLoad: false,
      ajax: {
        query: async (_: never, formValues: { id: number }) => {
          return await getCompanyRolePageApi({
            ...formValues,
          });
        },
      },
    },
    ...DETAIL_GRID_OPTIONS,
  },
  gridEvents: {
    checkboxChange: ({ checked, row }: { checked: boolean; row: CompanyInfo }) => {
      if (checked) {
        businessRoleGridApi.grid.setAllCheckboxRow(false);
        businessRoleGridApi.grid.setCheckboxRow(row, true);
      }
    },
  },
});
const [Modal, modalApi] = useVbenModal({
  centered: true,
  showConfirmButton: false,
  cancelText: '关闭',
  onOpened() {
    businessRoleGridApi.reload({ id: modalApi.getData().id });
  },
  onClosed() {
    gridApi.reload();
  },
});
const searchCompanyList = ref([]);
const searchCompanyForm = ref({
  keyword: '',
});
const searchCompanyFormRef = ref();
const [SearchCompanyModal, searchCompanyModalApi] = useVbenModal({
  centered: true,
  contentClass: 'min-h-12',
  async onConfirm() {
    await searchCompanyFormRef.value.validate();
    await searchCompanyModalApi.close();
    openFormPopup(true, { keyword: searchCompanyForm.value.keyword });
  },
  onClosed() {
    searchCompanyForm.value.keyword = '';
    searchCompanyList.value = [];
  },
});
const handleSearch = debounce(async (val: string) => {
  if (!val) {
    searchCompanyList.value = [];
    return false;
  }
  let resList = [];
  try {
    state.fetching = true;
    const { data: res = [] } = (await searchCompanyListApi({ keyword: val })) ?? [];
    resList = res.map((item: any) => ({
      label: item.name,
      value: item.name,
      ...item,
    }));
  } finally {
    state.fetching = false;
  }
  searchCompanyList.value = resList;
}, 800);
const skipSearch = () => {
  searchCompanyModalApi.close();
  openFormPopup(true, {});
};
const setBusinessRole = (row: CompanyInfo) => {
  modalApi.setData(row).open();
};
const addBusinessRole = async () => {
  const res = await getBusinessRoleListApi();
  const companyId = modalApi.getData().id;
  prompt({
    centered: true,
    component: Select,
    componentProps: {
      options: res,
      placeholder: '请选择',
      popupClassName: 'pointer-events-auto',
      fieldNames: { label: 'roleName', value: 'roleCode' },
    },
    title: '新增角色',
    content: '请选择角色',
    modelPropName: 'value',
    beforeClose: (scope): boolean => {
      const valid = !(scope.isConfirm && !scope.value);
      if (!valid) {
        message.error('请选择角色');
      }
      return valid;
    },
  }).then(async (val) => {
    if (val) {
      await addCompanyRoleApi({
        companyId,
        role: val,
      });
      message.success($t('base.resSuccess'));
      await businessRoleGridApi.reload({ id: companyId });
    }
  });
};
const removeBusinessRole = () => {
  const companyId = modalApi.getData().id;
  const res = businessRoleGridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  const id = res[0].id;
  AntdModal.confirm({
    title: '确认取消',
    content: '确认取消此业务角色吗？',
    async onOk() {
      await deleteCompanyRoleApi(id);
      message.success($t('base.resSuccess'));
      await businessRoleGridApi.reload({ id: companyId });
    },
  });
};
const handleBusinessRole = (_row: AccessInfo) => {};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-space>
          <a-button type="primary" @click="add()">新增</a-button>
          <a-button type="primary" @click="changeMaterialStatus(1)">启用</a-button>
          <a-button type="primary" danger @click="changeMaterialStatus(0)">禁用</a-button>
          <a-button type="primary" danger @click="delMaterial()">删除</a-button>
        </a-space>
      </template>
      <template #roles="{ row }">
        <a-tag v-for="item in row.roleList" :key="item.id" :color="item.status === 'EFFECTIVE' ? 'blue' : ''">
          {{ item.name }}
        </a-tag>
      </template>
      <template #action="{ row }">
        <a-space>
          <a-typography-link @click="edit(row)">编辑</a-typography-link>
          <a-typography-link v-if="!['BLACKLIST'].includes(row.status)" @click="setBusinessRole(row)">
            业务角色
          </a-typography-link>
          <a-typography-link @click="viewDetail(row)">详情</a-typography-link>
        </a-space>
      </template>
    </Grid>
    <CompanyEdit @register="registerForm" @ok="editSuccess" />
    <CompanyDetail @register="registerDetail" />
    <Modal title="业务角色" class="w-[800px]">
      <a-alert message="提示：如业务角色已失效，请先手动取消角色，再重新添加以恢复企业业务角色。" type="info" />
      <BusinessRoleGrid>
        <template #toolbar-actions>
          <a-space>
            <a-button type="primary" @click="addBusinessRole()">新增角色</a-button>
            <a-button type="primary" danger @click="removeBusinessRole()">取消角色</a-button>
          </a-space>
        </template>
        <template #action="{ row }">
          <a-space>
            <a-typography-link @click="handleBusinessRole(row)">补充材料</a-typography-link>
          </a-space>
        </template>
      </BusinessRoleGrid>
    </Modal>
    <SearchCompanyModal title="查询企业" class="w-[800px]">
      <a-form ref="searchCompanyFormRef" :model="searchCompanyForm">
        <a-form-item label="企业关键词" :rules="[{ required: true, message: '请输入企业关键词', trigger: 'change' }]">
          <a-select
            v-model:value="searchCompanyForm.keyword"
            show-search
            :options="searchCompanyList"
            :dropdown-match-select-width="false"
            :filter-option="false"
            :list-height="512"
            placement="bottomLeft"
            @search="handleSearch"
          >
            <template v-if="state.fetching" #notFoundContent>
              <a-spin size="small" />
            </template>
            <template #option="option">
              {{ option.name }} | 社会信用代码：{{ option.credit_no }} | 注册号：{{ option.reg_no }} | 法人：{{
                option.oper_name
              }}
            </template>
          </a-select>
        </a-form-item>
      </a-form>
      <template #center-footer>
        <a-button @click="skipSearch">跳过查询</a-button>
      </template>
    </SearchCompanyModal>
  </Page>
</template>

<style></style>
