<script lang="ts" setup>
import type { VbenFormSchema } from '@vben/common-ui';

import { computed } from 'vue';
import { useRoute } from 'vue-router';

import { AuthenticationLogin, z } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { useSystemConfigStore } from '@vben/stores';

import { checkBehaviorCaptchaApi, getBehaviorCaptchaApi, getImageCaptchaApi } from '#/api';
import { useAuthStore } from '#/store';

defineOptions({ name: 'Login' });

const { systemConfig } = useSystemConfigStore();

const authStore = useAuthStore();

const formSchema = computed((): VbenFormSchema[] => {
  return [
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: $t('authentication.usernameTip'),
      },
      fieldName: 'username',
      label: $t('authentication.username'),
      rules: z.string().min(1, { message: $t('authentication.usernameTip') }),
    },
    {
      component: 'VbenInputPassword',
      componentProps: {
        placeholder: $t('authentication.password'),
      },
      fieldName: 'password',
      label: $t('authentication.password'),
      rules: z.string().min(1, { message: $t('authentication.passwordTip') }),
    },
  ];
});
function removeTicketFromUrl(url: string) {
  try {
    const urlObj = new URL(url);
    const params = urlObj.searchParams;

    // 检查并删除 'ticket' 参数
    if (params.has('ticket')) {
      params.delete('ticket');
    }

    // 重新组合 URL，searchParams.toString() 会自动处理编码
    urlObj.search = params.toString();

    return urlObj.toString();
  } catch (error) {
    console.error('Invalid URL:', error);
    return url; // 如果 URL 无效，返回原始 URL
  }
}
const ssoLoginPath = computed(() => {
  const params = new URLSearchParams();
  params.append('service', removeTicketFromUrl(window.location.href));
  return `${import.meta.env.VITE_SSO_LOGIN_URL}?${params.toString()}`;
});
const route = useRoute();
const ticket = route.query.ticket as string;
if (ticket) {
  authStore.authLogin({
    grant_type: 'jxct_sso',
    ticket,
    service: encodeURIComponent(removeTicketFromUrl(window.location.href)),
  });
}
const source = route.query.source as string;
if (source) {
  try {
    authStore.authLogin({
      grant_type: 'social',
      source,
      code: route.query.code as string,
      state: route.query.state as string,
    });
  } finally {
    window.history.replaceState({}, '', location.pathname);
  }
}
const thirdPartyLogin = async (source: string) => {
  try {
    const currentUrl = new URL(window.location.href);
    currentUrl.searchParams.set('source', source);
    const redirectUri = currentUrl.toString();
    // 进行跳转
    window.location.href = `${import.meta.env.VITE_GLOB_API_URL}/auth/oauth/render/${source}?redirectUri=${redirectUri}`;
  } catch (error) {
    console.error('社交登录处理失败:', error);
  }
};
</script>

<template>
  <AuthenticationLogin
    :form-schema="formSchema"
    :show-captcha="systemConfig.verificationCodeSwitch"
    :captcha-type="systemConfig.verificationCodeType"
    :loading="authStore.loginLoading"
    :image-captcha-api="getImageCaptchaApi"
    :behavior-captcha-api="getBehaviorCaptchaApi"
    :behavior-captcha-check-api="checkBehaviorCaptchaApi"
    :sso-login-path="ssoLoginPath"
    @submit="authStore.authLogin"
    @third-party-login="thirdPartyLogin"
  />
</template>
