<script setup lang="ts">
import type { Recordable } from '@vben/types';

import { watch } from 'vue';

import { useDictStore } from '@vben/stores';

import { message } from 'ant-design-vue';

import { useVbenForm, z } from '#/adapter/form';
import { editUserProfileApi } from '#/api';

const props = defineProps<{
  profile?: object;
}>();
const emit = defineEmits<{
  (e: 'success'): void;
}>();
const dictStore = useDictStore();
const [Form, formApi] = useVbenForm({
  commonConfig: {
    labelWidth: 70,
  },
  schema: [
    {
      label: '姓名',
      fieldName: 'realName',
      component: 'Input',
      componentProps: {
        placeholder: '请输入姓名',
      },
      rules: 'required',
    },
    {
      label: '电话',
      fieldName: 'phone',
      component: 'Input',
      componentProps: {
        placeholder: '请输入电话',
      },
      rules: z.string(),
    },
    {
      label: '邮箱',
      fieldName: 'email',
      component: 'Input',
      componentProps: {
        placeholder: '请输入邮箱',
      },
      rules: z.string().email('请输入正确的邮箱'),
    },
    {
      label: '性别',
      fieldName: 'gender',
      component: 'RadioGroup',
      componentProps: {
        options: dictStore.getDictList('genderType'),
        buttonStyle: 'solid',
        optionType: 'button',
      },
    },
  ],
  resetButtonOptions: {
    show: false,
  },
  submitButtonOptions: {
    content: '更新信息',
  },
  handleSubmit,
});

async function handleSubmit(values: Recordable<any>) {
  try {
    formApi.setState({
      submitButtonOptions: {
        loading: true,
      },
    });
    // 提交表单
    await editUserProfileApi(values);
    // 关闭并提示
    emit('success');
    message.success('更新成功');
  } catch (error) {
    console.error(error);
  } finally {
    formApi.setState({
      submitButtonOptions: {
        loading: false,
      },
    });
  }
}

/** 监听 profile 变化 */
watch(
  () => props.profile,
  (newProfile) => {
    if (newProfile) {
      formApi.setValues(newProfile);
    }
  },
  { immediate: true },
);
</script>

<template>
  <div class="mt-4 md:w-full lg:w-1/2 2xl:w-2/5">
    <Form />
  </div>
</template>
