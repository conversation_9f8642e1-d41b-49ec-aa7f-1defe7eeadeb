<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { InitiationInfo, PaymentApplyInfo } from '#/api';

import { reactive, ref } from 'vue';

import { ApiComponent } from '@vben/common-ui';
import { BASE_PAGE_CLASS_NAME, COL_SPAN_PROP, FORM_PROP, FULL_FORM_ITEM_PROP } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';
import { useDictStore } from '@vben/stores';

import { message, Select } from 'ant-design-vue';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash-es';

import { BaseAttachmentList } from '#/adapter/base-ui';
import {
  addPaymentApplyApi,
  editPaymentApplyApi,
  getCompanyBankListApi,
  getCompanyDetailByCodeApi,
  getCompanyListApi,
  getCreditApplyListApi,
  getPaymentApplyInfoApi,
  getProjectLimitApi,
  getProjectListApi,
} from '#/api';

const emit = defineEmits(['ok', 'register']);
const dictStore = useDictStore();
const init = async (data: PaymentApplyInfo) => {
  applyForm.value = {};
  applyForm.value.itemList = itemList;
  if (data.id) {
    const info = data.id ? await getPaymentApplyInfoApi(data.id as number) : data;
    info.expectInvestDate = dayjs(info.expectInvestDate).valueOf().toString();
    applyForm.value = info;
    await getCompanyBankList();
  } else {
    applyForm.value.payerCompanyCode = '91361200MAC5TQUL5M';
    const companyDetail = await getCompanyDetailByCodeApi({ code: applyForm.value.payerCompanyCode });
    applyForm.value.payerCompanyName = companyDetail.companyName;
    applyForm.value.expectInvestDate = dayjs().format('x');
  }
  await gridApi.grid.reloadData(applyForm.value.itemList);
};
const [registerPopup, { closePopup }] = usePopupInner(init);
const FormRef = ref();
const applyForm = ref<PaymentApplyInfo>({});
const itemList = ref([
  {
    implementationMatters: '1.保理业务申请书',
    isImplemented: '',
    materialRequirements: '债权人盖章',
    operationsAffairs: '法律文件签署',
    remarks: '',
  },
  {
    implementationMatters: '2.债权人股东同意开展保理融资业务的股东决议',
    isImplemented: '',
    materialRequirements: '债权人股东盖章',
    operationsAffairs: '法律文件签署',
    remarks: '',
  },
  {
    implementationMatters: '3.保理合同（无需取得应收账款债务人回执）',
    isImplemented: '',
    materialRequirements: '债权人、保理公司盖章，法人章',
    operationsAffairs: '法律文件签署',
    remarks: '',
  },
  {
    implementationMatters: '4.担保方同意保证担保以及抵押担保的董事会决议',
    isImplemented: '',
    materialRequirements: '担保方董事会签字，担保方盖章',
    operationsAffairs: '法律文件签署',
    remarks: '',
  },
  {
    implementationMatters: '5.保证合同',
    isImplemented: '',
    materialRequirements: '担保方及保理公司盖章',
    operationsAffairs: '法律文件签署',
    remarks: '',
  },
  {
    implementationMatters: '6.抵押合同',
    isImplemented: '',
    materialRequirements: '抵押人、抵押权人盖章，法人章',
    operationsAffairs: '法律文件签署',
    remarks: '',
  },
  {
    implementationMatters: '7.不涉隐债承诺函',
    isImplemented: '',
    materialRequirements: '债权人、债务人、保理人（抵押人）盖章',
    operationsAffairs: '法律文件签署',
    remarks: '',
  },
  {
    implementationMatters: '8.廉洁协议',
    isImplemented: '',
    materialRequirements: '债权人、保证人（抵押人）盖章',
    operationsAffairs: '法律文件签署',
    remarks: '',
  },
  {
    implementationMatters: '9.工程进度计量产值材料最新一期或能体现供货使用的相关材料',
    isImplemented: '',
    materialRequirements: '',
    operationsAffairs: '材料补充',
    remarks: '',
  },
  {
    implementationMatters: '10.中铁十五局委托协议或者情况说明',
    isImplemented: '',
    materialRequirements: '',
    operationsAffairs: '材料补充',
    remarks: '',
  },
  {
    implementationMatters: '11.取得抵押物他项权证（现场访谈确定抵押物一致后办理）',
    isImplemented: '',
    materialRequirements: '他项权证',
    operationsAffairs: '他项权证',
    remarks: '',
  },
  {
    implementationMatters: '12.登记转让的应收账款以及登记附件',
    isImplemented: '',
    materialRequirements: '需登记到底层购销合同号及销项发票号',
    operationsAffairs: '应收账款登记',
    remarks: '',
  },
]);
const loading = reactive({
  submit: false,
});
const baseGridOptions = {
  showOverflow: true,
  keepSource: true,
  editConfig: {
    trigger: 'click',
    mode: 'row',
    autoClear: false,
  },
  rowConfig: {
    drag: true,
  },
  pagerConfig: {
    enabled: false,
  },
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const gridOptions = {
  mergeCells: [
    { row: 0, col: 0, rowspan: 8, colspan: 1 },
    { row: 8, col: 0, rowspan: 2, colspan: 1 },
  ],
  columns: [
    {
      field: 'operationsAffairs',
      title: '运营事务',
      width: '120px',
    },
    {
      field: 'implementationMatters',
      title: '需落实事项',
      minWidth: '280px',
    },
    {
      field: 'materialRequirements',
      title: '材料要求',
      minWidth: '200px',
    },
    {
      field: 'isImplemented',
      title: '是否已落实？',
      slots: { default: 'edit_is_implemented' },
      width: '120px',
    },
    {
      field: 'remarks',
      title: '备注',
      slots: { default: 'edit_remarks' },
      width: '160px',
    },
  ],
  ...baseGridOptions,
} as VxeTableGridOptions;
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});
const save = async (type: string) => {
  await FormRef.value.validate();
  let api = addPaymentApplyApi;
  if (applyForm.value.id) {
    api = editPaymentApplyApi;
  }
  const formData = cloneDeep(applyForm.value);
  formData.expectInvestDate = Number(formData.expectInvestDate);
  loading.submit = true;
  if (type === 'submit') {
    formData.isSubmit = true;
    formData.processDefinitionKey = 'fct_payment_apply';
  }
  try {
    const res = await api(formData as PaymentApplyInfo);
    message.success('保存成功');
    closePopup();
    emit('ok', res);
  } finally {
    loading.submit = false;
  }
};
const selectInitiation = (_value: number, data: InitiationInfo) => {
  applyForm.value.projectType = data.projectType;
  applyForm.value.projectName = data.label;
  // if (applyForm.value.projectType === 'single') {
  //   applyForm.value.investAmount = data.creditCalculateAmount || '';
  // }
};
const selectCreditApply = (_value: number, data: PaymentApplyInfo) => {
  applyForm.value.projectCreditApplyName = data.label;
  applyForm.value.projectApplyAmount = data.applyAmount;
  // applyForm.value.investAmount = data.applyAmount;
  getProjectLimit({ projectId: applyForm.value.projectId, projectCreditApplyId: applyForm.value.projectCreditApplyId });
};
const payeeBankOptions = ref<any[]>([]);
const payeeCompanyCodeChange = async (_value: number, data: CompanyInfo) => {
  if (applyForm.value.payeeBankId) applyForm.value.payeeBankId = '';
  if (applyForm.value.payeeBankAccount) applyForm.value.payeeBankAccount = '';
  applyForm.value.payeeCompanyName = data.label;
  getCompanyBankList();
};
const getCompanyBankList = async () => {
  const res = await getCompanyBankListApi({ code: applyForm.value.payeeCompanyCode });
  payeeBankOptions.value = res;
};
const selectBankBranch = (_value: number, data: object) => {
  applyForm.value.payeeBankAccount = data.account;
  applyForm.value.payeeBankBranch = data.accountName;
};
const getProjectLimit = async (data: { projectCreditApplyId: number; projectId: number }) => {
  const res = await getProjectLimitApi(data);
  const { id: _, ...info } = res;
  applyForm.value = { ...applyForm.value, ...info };
};
const rules = {
  projectId: [{ required: true, message: '请选择关联项目', trigger: 'change' }],
  // projectCreditApplyId: [{ required: true, message: '请选择关联用信申请', trigger: 'change' }],
  paymentMethod: [{ required: true, message: '请选择付款方式', trigger: 'change' }],
  expectInvestDate: [{ required: true, message: '请选择预计投放日期', trigger: 'change' }],
  investAmount: [{ required: true, message: '请输入投放金额', trigger: 'blur' }],
  payeeCompanyCode: [{ required: true, message: '请选择收款单位', trigger: 'change' }],
  payeeBankAccount: [{ required: true, message: '请输入收款单位银行账号', trigger: 'blur' }],
  payeeBankId: [{ required: true, message: '请选择收款单位开户行名称', trigger: 'change' }],
};
const formProp = { ...FORM_PROP, labelCol: { span: 6 }, wrapperCol: { span: 18 } };
const colSpan = COL_SPAN_PROP;
const fullProp = { ...FULL_FORM_ITEM_PROP, labelCol: { span: 3 }, wrapperCol: { span: 21 } };
</script>

<template>
  <BasicPopup v-bind="$attrs" title="付款申请" @register="registerPopup">
    <template #insertToolbar>
      <a-space>
        <a-button type="primary" :loading="loading.submit" @click="save('save')">保存</a-button>
        <a-button type="primary" :loading="loading.submit" @click="save('submit')">提交</a-button>
      </a-space>
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-form ref="FormRef" class="mt-5" :model="applyForm" :rules="rules" v-bind="formProp">
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="关联项目" name="projectId">
              <ApiComponent
                v-model="applyForm.projectId as unknown as string"
                :component="Select"
                :api="getProjectListApi"
                :params="{ status: 'EFFECTIVE', isMeetingCompleted: 1 }"
                label-field="projectName"
                value-field="id"
                model-prop-name="value"
                @change="selectInitiation"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan" v-show="applyForm.projectType === 'comprehensive'">
            <a-form-item label="关联用信申请" name="projectCreditApplyId">
              <ApiComponent
                v-model="applyForm.projectCreditApplyId as unknown as string"
                :component="Select"
                :api="getCreditApplyListApi"
                :params="{ projectId: applyForm.projectId }"
                label-field="projectCreditApplyName"
                value-field="id"
                model-prop-name="value"
                :immediate="false"
                @change="selectCreditApply"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="付款申请编号">
              <a-input v-model:value="applyForm.applyCode" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="付款单位">
              <a-input v-model:value="applyForm.payerCompanyName" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan" v-if="applyForm.projectType === 'comprehensive'">
            <a-form-item label="用信申请金额（元）">
              <a-input v-model:value="applyForm.projectApplyAmount" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="投放金额（元）" name="investAmount">
              <a-input v-model:value="applyForm.investAmount" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="付款方式" name="paymentMethod">
              <a-radio-group
                v-model:value="applyForm.paymentMethod"
                :options="dictStore.getDictList('FCT_PAYMENT_METHOD')"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="预计投放日期" name="expectInvestDate">
              <a-date-picker v-model:value="applyForm.expectInvestDate" value-format="x" class="w-full" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="备注" name="remarks" v-bind="fullProp">
              <a-textarea v-model:value="applyForm.remarks" :rows="4" class="w-full" />
            </a-form-item>
          </a-col>
        </a-row>
        <BasicCaption content="放款前落实事项" />
        <Grid>
          <template #edit_is_implemented="{ row }">
            <a-radio-group v-model:value="row.isImplemented" :options="dictStore.getDictList('baseBooleanType')" />
          </template>
          <template #edit_remarks="{ row }">
            <a-input v-model:value="row.remarks" class="w-full" />
          </template>
        </Grid>
        <template v-if="applyForm.projectType === 'comprehensive'">
          <BasicCaption content="授信及额度信息" />
          <a-row class="mt-5">
            <a-col v-bind="colSpan">
              <a-form-item label="授信额度（元）">
                <a-input v-model:value="applyForm.creditAmount" disabled />
              </a-form-item>
            </a-col>
            <a-col v-bind="colSpan">
              <a-form-item label="授信利率（%/年）">
                <a-input v-model:value="applyForm.creditRate" disabled />
              </a-form-item>
            </a-col>
            <a-col v-bind="colSpan">
              <a-form-item label="授信企业">
                <a-input v-model:value="applyForm.creditCompanyName" disabled />
              </a-form-item>
            </a-col>
            <a-col v-bind="colSpan">
              <a-form-item label="授信额度类型">
                {{ dictStore.formatter(applyForm.creditType, 'FCT_CREDIT_TYPE') }}
              </a-form-item>
            </a-col>
            <a-col v-bind="colSpan">
              <a-form-item label="授信批复时间">
                {{ applyForm.creditApprovalDate }}
              </a-form-item>
            </a-col>
            <a-col v-bind="colSpan">
              <a-form-item label="授信期限（个月）">
                <a-input v-model:value="applyForm.creditTerm" disabled />
              </a-form-item>
            </a-col>
            <a-col v-bind="colSpan">
              <a-form-item label="已用额度（元）">
                <a-input v-model:value="applyForm.usedAmount" disabled />
              </a-form-item>
            </a-col>
            <a-col v-bind="colSpan">
              <a-form-item label="可用额度（元）">
                <a-input v-model:value="applyForm.availableAmount" disabled />
              </a-form-item>
            </a-col>
          </a-row>
        </template>
        <BasicCaption content="确认收款单位信息" />
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="收款单位" name="payeeCompanyCode">
              <ApiComponent
                v-model="applyForm.payeeCompanyCode as unknown as string"
                :component="Select"
                :api="getCompanyListApi"
                label-field="companyName"
                value-field="companyCode"
                model-prop-name="value"
                @change="payeeCompanyCodeChange"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="收款单位开户行名称" name="payeeBankId">
              <a-select
                v-model:value="applyForm.payeeBankId"
                :options="payeeBankOptions"
                :field-names="{ label: 'accountName', value: 'id' }"
                @change="selectBankBranch"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="收款单位银行账号" name="payeeBankAccount">
              <a-input v-model:value="applyForm.payeeBankAccount" />
            </a-form-item>
          </a-col>
        </a-row>
        <BaseAttachmentList
          v-model="applyForm.attachmentList"
          :business-id="applyForm.id"
          business-type="FCT_PAYMENT_APPLY"
          edit-mode
        />
      </a-form>
    </div>
  </BasicPopup>
</template>

<style></style>
