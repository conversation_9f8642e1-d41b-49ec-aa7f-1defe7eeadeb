<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { PaymentApplyInfo } from '#/api';

import { ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP, DETAIL_GRID_OPTIONS } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';
import { useDictStore } from '@vben/stores';
import { formatDate } from '@vben/utils';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { getPaymentApplyInfoApi, getProjectLimitApi } from '#/api';
import { useWorkflowBase } from '#/composables/useWorkflowBase';

const emit = defineEmits(['ok', 'register']);
const {
  WorkflowPreviewModal,
  useOperationButton,
  useViewButton,
  initWorkflow,
  isWorkflow,
  isRunningTask,
  isWorkflowLoading,
} = useWorkflowBase();

const OperationButton = useOperationButton();
const ViewButton = useViewButton();
const pageType = ref('detail');
const dictStore = useDictStore();
const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: {
    width: '220px',
    justifyContent: 'flex-end',
  },
};
const init = async (data: PaymentApplyInfo) => {
  pageType.value = data.pageType ?? 'detail';
  await initWorkflow({ formKey: 'fct_payment_apply', businessKey: data.id });
  applyForm.value = data.id ? await getPaymentApplyInfoApi(data.id as number) : { ...data };
  await getProjectLimit({
    projectId: applyForm.value.projectId,
    projectCreditApplyId: applyForm.value.projectCreditApplyId,
  });
  await gridApi.grid.reloadData(applyForm.value.itemList);
};
const getProjectLimit = async (data: { projectCreditApplyId: number; projectId: number }) => {
  const res = await getProjectLimitApi(data);
  const { id: _, ...info } = res;
  applyForm.value = { ...applyForm.value, ...info };
};
const workflowSuccess = () => {
  emit('ok');
  closePopup();
};
const [registerPopup, { closePopup }] = usePopupInner(init);
const gridOptions = {
  mergeCells: [
    { row: 0, col: 0, rowspan: 8, colspan: 1 },
    { row: 8, col: 0, rowspan: 2, colspan: 1 },
  ],
  columns: [
    {
      field: 'operationsAffairs',
      title: '运营事务',
      width: '120px',
    },
    {
      field: 'implementationMatters',
      title: '需落实事项',
      minWidth: '280px',
    },
    {
      field: 'materialRequirements',
      title: '材料要求',
      minWidth: '200px',
    },
    {
      field: 'isImplemented',
      title: '是否已落实？',
      formatter: ['formatStatus', 'baseBooleanType'],
      width: '120px',
    },
    {
      field: 'remarks',
      title: '备注',
      width: '160px',
    },
  ],
  ...DETAIL_GRID_OPTIONS,
} as VxeTableGridOptions;
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});
const applyForm = ref<PaymentApplyInfo>({});
</script>

<template>
  <BasicPopup title="付款申请详情" @register="registerPopup">
    <template #insertToolbar>
      <div v-if="pageType === 'audit'" v-loading="isWorkflowLoading">
        <OperationButton @success="workflowSuccess" />
      </div>
      <ViewButton v-if="isWorkflow && !isRunningTask && pageType === 'detail'" />
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions v-bind="descriptionsProp" class="mt-4">
        <a-descriptions-item label="关联项目">
          {{ applyForm.projectName }}
        </a-descriptions-item>
        <a-descriptions-item label="付款申请编号">
          {{ applyForm.applyCode }}
        </a-descriptions-item>
        <a-descriptions-item label="付款单位">
          {{ applyForm.payerCompanyName }}
        </a-descriptions-item>
        <a-descriptions-item label="用信申请金额（元）" v-if="applyForm.projectType === 'comprehensive'">
          {{ applyForm.projectApplyAmount }}
        </a-descriptions-item>
        <a-descriptions-item label="投放金额（元）">
          {{ applyForm.investAmount }}
        </a-descriptions-item>
        <a-descriptions-item label="付款方式">
          {{ dictStore.formatter(applyForm.paymentMethod, 'FCT_PAYMENT_METHOD') }}
        </a-descriptions-item>
        <a-descriptions-item label="预计投放日期">
          {{ formatDate(applyForm.expectInvestDate) }}
        </a-descriptions-item>
        <a-descriptions-item label="备注">
          {{ applyForm.remarks }}
        </a-descriptions-item>
      </a-descriptions>

      <BasicCaption content="放款前落实事项" />
      <Grid />
      <template v-if="applyForm.projectType === 'comprehensive'">
        <BasicCaption content="授信及额度信息" />
        <a-descriptions v-bind="descriptionsProp" class="mt-4">
          <a-descriptions-item label="授信额度（元）">
            {{ applyForm.creditAmount }}
          </a-descriptions-item>
          <a-descriptions-item label="授信利率（%/年）">
            {{ applyForm.creditRate }}
          </a-descriptions-item>
          <a-descriptions-item label="授信企业">
            {{ applyForm.creditCompanyName }}
          </a-descriptions-item>
          <a-descriptions-item label="授信额度类型">
            {{ dictStore.formatter(applyForm.creditType, 'FCT_CREDIT_TYPE') }}
          </a-descriptions-item>
          <a-descriptions-item label="授信批复时间">
            {{ formatDate(applyForm.creditApprovalDate) }}
          </a-descriptions-item>
          <a-descriptions-item label="授信期限（个月）">
            {{ applyForm.creditTerm }}
          </a-descriptions-item>
          <a-descriptions-item label="已用额度（元）">
            {{ applyForm.usedAmount }}
          </a-descriptions-item>
          <a-descriptions-item label="可用额度（元）">
            {{ applyForm.availableAmount }}
          </a-descriptions-item>
        </a-descriptions>
      </template>
      <BasicCaption content="确认收款单位信息" />
      <a-descriptions v-bind="descriptionsProp" class="mt-4">
        <a-descriptions-item label="收款单位">
          {{ applyForm.payeeCompanyName }}
        </a-descriptions-item>
        <a-descriptions-item label="收款单位开户行名称">
          {{ applyForm.payeeBankBranch }}
        </a-descriptions-item>
        <a-descriptions-item label="收款单位银行账号">
          {{ applyForm.payeeBankAccount }}
        </a-descriptions-item>
      </a-descriptions>
      <BaseAttachmentList :business-id="applyForm.id" business-type="FCT_PAYMENT_APPLY" />
    </div>
    <template v-if="pageType === 'audit'">
      <WorkflowPreviewModal />
    </template>
  </BasicPopup>
</template>

<style></style>
