<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { PaymentApplyInfo } from '#/api';

import { useModalUrl } from '@vben/base-ui';
import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { Modal as AntdModal, message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { delPaymentApplyApi, getPaymentApplyPageListApi } from '#/api';

import PaymentApplyDetail from './payment-apply-detail.vue';
import PaymentApplyEdit from './payment-apply-edit.vue';
import PaymentRecordEdit from './payment-record-edit.vue';

const dictStore = useDictStore();

const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '项目名称',
    },
    {
      component: 'Input',
      fieldName: 'projectCreditApplyName',
      label: '用信申请名称',
    },
    {
      component: 'Input',
      fieldName: 'payeeCompanyName',
      label: '收款单位',
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '操作状态',
      componentProps: {
        options: dictStore.getDictList('FCT_STATUS'),
      },
    },
    {
      component: 'Select',
      fieldName: 'reviewStatus',
      label: '审批状态',
      componentProps: {
        options: dictStore.getDictList('REVIEW_STATUS'),
      },
    },
  ],
  commonConfig: {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
  },
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'applyCode', title: '付款申请编号', minWidth: 180 },
    { field: 'projectName', title: '项目名称', minWidth: 200 },
    { field: 'projectCreditApplyName', title: '用信申请名称', minWidth: 200 },
    { field: 'investAmount', title: '投放金额（元）' },
    { field: 'payeeCompanyName', title: '收款单位' },
    {
      field: 'status',
      title: '操作状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_STATUS',
        },
      },
    },
    {
      field: 'reviewStatus',
      title: '审批状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'REVIEW_STATUS',
        },
      },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getPaymentApplyPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  checkboxConfig: {
    showHeader: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const add = () => {
  openFormPopup(true, {});
};
const edit = (row: PaymentApplyInfo) => {
  openFormPopup(true, row);
};
const payment = (row: PaymentApplyInfo) => {
  openRecordPopup(true, row);
};
const editSuccess = () => {
  gridApi.reload();
};
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [recordForm, { openPopup: openRecordPopup }] = usePopup();
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
const viewDetail = (row: PaymentApplyInfo) => {
  openDetailPopup(true, row);
};
const del = (row: PaymentApplyInfo) => {
  const id = row.id;
  AntdModal.confirm({
    title: '确认删除',
    content: '此操作将删除该付款申请，是否继续？',
    async onOk() {
      await delPaymentApplyApi(id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};

// 使用组合式函数处理URL参数自动打开弹层
useModalUrl({
  handlers: {
    // 编辑弹层
    edit: (params) => {
      const data: PaymentApplyInfo = {
        id: params.id,
      };
      edit(data);
    },

    // 详情弹层
    detail: (params) => {
      const data: PaymentApplyInfo = {
        id: params.id,
      };
      viewDetail(data);
    },

    // 审核弹层
    audit: (params) => {
      const data: PaymentApplyInfo = {
        id: params.id,
        pageType: 'audit',
      };
      audit(data);
    },
  },
});
const audit = (row: PaymentApplyInfo) => {
  const data = { ...row, pageType: 'audit' };
  openDetailPopup(true, data);
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-space>
          <a-button class="mr-2" type="primary" @click="add">
            {{ $t('base.add') }}
          </a-button>
        </a-space>
      </template>
      <template #action="{ row }">
        <a-space>
          <a-typography-link v-if="['SUBMIT'].includes(row.status)" @click="edit(row)">
            {{ $t('base.edit') }}
          </a-typography-link>
          <a-typography-link @click="viewDetail(row)">
            {{ $t('base.detail') }}
          </a-typography-link>
          <a-typography-link type="danger" v-if="['SUBMIT'].includes(row.status)" @click="del(row)">
            {{ $t('base.del') }}
          </a-typography-link>
          <a-typography-link
            v-if="['draft'].includes(row.confirmStatus) && ['EFFECTIVE'].includes(row.status)"
            @click="payment(row)"
          >
            记录付款
          </a-typography-link>
        </a-space>
      </template>
    </Grid>
    <PaymentApplyEdit @register="registerForm" @ok="editSuccess" />
    <PaymentRecordEdit @register="recordForm" @ok="editSuccess" />
    <PaymentApplyDetail @register="registerDetail" @ok="editSuccess" />
  </Page>
</template>

<style></style>
