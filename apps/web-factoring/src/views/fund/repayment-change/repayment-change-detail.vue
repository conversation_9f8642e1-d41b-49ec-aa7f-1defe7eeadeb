<script setup lang="ts">
import type { PaymentRecordInfo } from '#/api';

import { ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { getRepaymentChangeInfoApi } from '#/api';
import { useWorkflowBase } from '#/composables/useWorkflowBase';
import InterestFees from '#/views/fund/components/interest-fees.vue';
import RepaymentPlanDetail from '#/views/fund/components/repayment-plan-detail.vue';

const emit = defineEmits(['ok', 'register']);
const {
  WorkflowPreviewModal,
  useOperationButton,
  useViewButton,
  initWorkflow,
  isWorkflow,
  isRunningTask,
  isWorkflowLoading,
} = useWorkflowBase();

const OperationButton = useOperationButton();
const ViewButton = useViewButton();
const pageType = ref('detail');
const dictStore = useDictStore();

// 详情页样式配置
const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: {
    width: '220px',
    justifyContent: 'flex-end',
  },
};

// 初始化数据
const init = async (data: PaymentRecordInfo) => {
  // 获取详情数据，如果有ID则从接口获取，否则使用传入的数据
  pageType.value = data.pageType ?? 'detail';
  await initWorkflow({ formKey: 'fct_repayment_plan_change', businessKey: data.id });
  const info = data.id ? await getRepaymentChangeInfoApi(data.id as number) : data;
  changeForm.value = { ...changeForm.value, ...info };
};

const changeForm = ref<PaymentRecordInfo>({});
const workflowSuccess = () => {
  emit('ok');
  closePopup();
};
const [registerPopup, { closePopup }] = usePopupInner(init);
</script>

<template>
  <BasicPopup title="还款计划变更详情" @register="registerPopup">
    <template #insertToolbar>
      <div v-if="pageType === 'audit'" v-loading="isWorkflowLoading">
        <OperationButton @success="workflowSuccess" />
      </div>
      <ViewButton v-if="isWorkflow && !isRunningTask && pageType === 'detail'" />
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions v-bind="descriptionsProp" class="mt-4">
        <a-descriptions-item label="还款计划变更编号">
          {{ changeForm.repaymentPlanChangeCode }}
        </a-descriptions-item>
        <a-descriptions-item label="关联还款计划编号">
          {{ changeForm.repaymentPlanCode }}
        </a-descriptions-item>
        <a-descriptions-item label="项目名称">
          {{ changeForm.projectName }}
        </a-descriptions-item>
        <a-descriptions-item label="项目类型">
          {{ dictStore.formatter(changeForm.projectType, 'FCT_PROJECT_TYPE') }}
        </a-descriptions-item>
        <a-descriptions-item label="关联用信申请">
          {{ changeForm.creditApplyName }}
        </a-descriptions-item>
        <a-descriptions-item label="关联付款申请编号">
          {{ changeForm.paymentApplyCode }}
        </a-descriptions-item>
        <a-descriptions-item label="关联付款记录">
          {{ changeForm.paymentConfirmCode }}
        </a-descriptions-item>
      </a-descriptions>

      <!-- 利息费用详情 -->
      <InterestFees :interest-form="changeForm" />

      <!-- 还款计划详情 -->
      <RepaymentPlanDetail
        :calculation-form="changeForm"
        :descriptions-prop="descriptionsProp"
        calculation-type="RepaymentChange"
      />
      <BaseAttachmentList :business-id="changeForm.id" business-type="FCT_REPAYMENT_PLAN_CHANGE" />
    </div>
    <template v-if="pageType === 'audit'">
      <WorkflowPreviewModal />
    </template>
  </BasicPopup>
</template>

<style></style>
