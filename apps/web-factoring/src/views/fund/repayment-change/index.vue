<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { RepaymentChangeInfo } from '#/api';

import { useModalUrl } from '@vben/base-ui';
import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { defineFormOptions } from '@vben/utils';

import { Modal as AntdModal, message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { delRepaymentChangeApi, getRepaymentChangePageListApi } from '#/api';

import RepaymentChangeDetail from './repayment-change-detail.vue';
import RepaymentChangeEdit from './repayment-change-edit.vue';

const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'repaymentPlanChangeCode',
      label: '还款计划变更编号',
    },
    {
      component: 'Input',
      fieldName: 'repaymentPlanCode',
      label: '还款计划编号',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '项目名称',
    },
    {
      component: 'Input',
      fieldName: 'creditApplyName',
      label: '用信申请名称',
    },
    {
      component: 'Input',
      fieldName: 'paymentApplyCode',
      label: '付款申请编号',
    },
    {
      component: 'Input',
      fieldName: 'paymentConfirmCode',
      label: '付款记录编号',
    },
  ],
  commonConfig: {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
  },
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'repaymentPlanChangeCode', title: '还款计划变更编号', minWidth: 180 },
    { field: 'repaymentPlanCode', title: '关联还款计划编号', minWidth: 180 },
    { field: 'projectName', title: '项目名称', minWidth: 200 },
    {
      field: 'projectType',
      title: '项目类型',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_PROJECT_TYPE',
        },
      },
    },
    { field: 'creditApplyName', title: '关联用信申请', minWidth: 200 },
    { field: 'paymentApplyCode', title: '关联付款申请编号', minWidth: 180 },
    { field: 'paymentConfirmCode', title: '付款记录编号', minWidth: 200 },
    {
      field: 'status',
      title: '操作状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_STATUS',
        },
      },
    },
    {
      field: 'reviewStatus',
      title: '审批状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'REVIEW_STATUS',
        },
      },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 140,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getRepaymentChangePageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const add = () => {
  openFormPopup(true, {});
};
const edit = (row: RepaymentChangeInfo) => {
  openFormPopup(true, row);
};
const editSuccess = () => {
  gridApi.reload();
};
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
const viewDetail = (row: RepaymentChangeInfo) => {
  openDetailPopup(true, row);
};
const del = (row: RepaymentChangeInfo) => {
  const id = row.id;
  AntdModal.confirm({
    title: '确认删除',
    content: '此操作将删除该还款变更，是否继续？',
    async onOk() {
      await delRepaymentChangeApi(id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};

// 使用组合式函数处理URL参数自动打开弹层
useModalUrl({
  handlers: {
    // 编辑弹层
    edit: (params) => {
      const data: RepaymentChangeInfo = {
        id: params.id,
      };
      edit(data);
    },

    // 详情弹层
    detail: (params) => {
      const data: RepaymentChangeInfo = {
        id: params.id,
      };
      viewDetail(data);
    },

    // 审核弹层
    audit: (params) => {
      const data: RepaymentChangeInfo = {
        id: params.id,
        pageType: 'audit',
      };
      audit(data);
    },
  },
});
const audit = (row: RepaymentChangeInfo) => {
  const data = { ...row, pageType: 'audit' };
  openDetailPopup(true, data);
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-space>
          <a-button class="mr-2" type="primary" @click="add">
            {{ $t('base.add') }}
          </a-button>
        </a-space>
      </template>
      <template #action="{ row }">
        <a-space>
          <a-typography-link v-if="['SUBMIT'].includes(row.status)" @click="edit(row)">
            {{ $t('base.edit') }}
          </a-typography-link>
          <a-typography-link @click="viewDetail(row)">
            {{ $t('base.detail') }}
          </a-typography-link>
          <a-typography-link type="danger" v-if="['SUBMIT'].includes(row.status)" @click="del(row)">
            {{ $t('base.del') }}
          </a-typography-link>
        </a-space>
      </template>
    </Grid>
    <RepaymentChangeEdit @register="registerForm" @ok="editSuccess" />
    <RepaymentChangeDetail @register="registerDetail" @ok="editSuccess" />
  </Page>
</template>

<style></style>
