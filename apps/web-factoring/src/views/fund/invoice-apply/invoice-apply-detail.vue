<script setup lang="ts">
import type { PaymentRecordInfo } from '#/api';

import { ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';
import { formatDate } from '@vben/utils';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { getInvoiceApplyInfoApi } from '#/api';
import { useWorkflowBase } from '#/composables/useWorkflowBase';
import InvoiceContent from '#/views/fund/components/invoice-content.vue';

const emit = defineEmits(['ok', 'register']);
const {
  WorkflowPreviewModal,
  useOperationButton,
  useViewButton,
  initWorkflow,
  isWorkflow,
  isRunningTask,
  isWorkflowLoading,
} = useWorkflowBase();

const OperationButton = useOperationButton();
const ViewButton = useViewButton();
const pageType = ref('detail');
const dictStore = useDictStore();

// 详情页样式配置
const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: {
    width: '220px',
    justifyContent: 'flex-end',
  },
};

// 初始化数据
const init = async (data: PaymentRecordInfo) => {
  // 获取详情数据，如果有ID则从接口获取，否则使用传入的数据
  pageType.value = data.pageType ?? 'detail';
  await initWorkflow({ formKey: 'fct_invoice', businessKey: data.id });
  const info = data.id ? await getInvoiceApplyInfoApi(data.id as number) : data;
  invoiceForm.value = { ...invoiceForm.value, ...info };
};

const invoiceForm = ref<PaymentRecordInfo>({});
const workflowSuccess = () => {
  emit('ok');
  closePopup();
};
const [registerPopup, { closePopup }] = usePopupInner(init);
</script>

<template>
  <BasicPopup title="开票申请详情" @register="registerPopup">
    <template #insertToolbar>
      <div v-if="pageType === 'audit'" v-loading="isWorkflowLoading">
        <OperationButton @success="workflowSuccess" />
      </div>
      <ViewButton v-if="isWorkflow && !isRunningTask && pageType === 'detail'" />
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions v-bind="descriptionsProp" class="mt-4">
        <a-descriptions-item label="项目名称">
          {{ invoiceForm.projectName }}
        </a-descriptions-item>

        <a-descriptions-item label="开票项">
          {{ dictStore.formatter(invoiceForm.invoiceItem, 'FCT_INVOICE_ITME') }}
        </a-descriptions-item>

        <a-descriptions-item label="开票税率(%)">
          {{ invoiceForm.invoiceTax }}
        </a-descriptions-item>

        <a-descriptions-item
          :label="`待开票${invoiceForm.invoiceItem === 'service_fee' ? '付款记录编号' : '还款记录编号'}`"
        >
          <!-- 显示选中的记录编号，这里假设关联记录信息存在于refList中 -->
          <template v-if="invoiceForm.refList && invoiceForm.refList.length > 0">
            <span v-for="(item, index) in invoiceForm.refList" :key="index">
              {{ item.repaymentConfirmCode || item.paymentConfirmCode }}
              <template v-if="index < invoiceForm.refList.length - 1">、</template>
            </span>
          </template>
        </a-descriptions-item>

        <a-descriptions-item label="发票类型">
          {{ dictStore.formatter(invoiceForm.invoiceType, 'FCT_INVOICE_TYPE') }}
        </a-descriptions-item>

        <a-descriptions-item label="购买方名称">
          {{ invoiceForm.buyerName }}
        </a-descriptions-item>

        <a-descriptions-item label="税号">
          {{ invoiceForm.taxNumber }}
        </a-descriptions-item>

        <a-descriptions-item label="开票申请日期">
          {{ formatDate(invoiceForm.applicationDate) }}
        </a-descriptions-item>

        <a-descriptions-item label="备注" :span="4">
          {{ invoiceForm.remarks }}
        </a-descriptions-item>
      </a-descriptions>

      <InvoiceContent :invoice-form="invoiceForm" />
      <BaseAttachmentList :business-id="invoiceForm.id" business-type="FCT_INVOICE_OPEN" />
    </div>
    <template v-if="pageType === 'audit'">
      <WorkflowPreviewModal />
    </template>
  </BasicPopup>
</template>

<style></style>
