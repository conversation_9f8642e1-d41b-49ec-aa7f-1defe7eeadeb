<script setup lang="ts">
import type { areaComputeVo } from '#/api';

import { computed, reactive, ref } from 'vue';

import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import { cloneDeep } from '@vben/utils';

import { Button } from 'ant-design-vue';

import { useWorkflowBase } from '#/composables/useWorkflowBase';

import AreaInfo from '../components/areaInfo.vue';

const emit = defineEmits(['register', 'ok']);
const { WorkflowPreviewModal, startWorkflow, initWorkflow } = useWorkflowBase();

const baseFormInfo = reactive<areaComputeVo>({
  adjustSupportingFileId: 0,
  budgetAmount: undefined,
  cityCode: '',
  province: '江西省',
  cityName: '',
  companyRating: '',
  creditAmount: 0,
  creditRatio: 0,
  creditScore: 0,
  districtCode: '',
  isSubmit: false,
  districtName: '',
  id: undefined,
  limitRuleList: [
    {
      quotaCategory: '区域情况',
      quotaName: '人口（万人）',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: '财政平衡率（%）',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: 'GDP增速（%）',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: '财政实力（%）',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: '区域利差',
      dictType: 'FCT_LIMIT_INTEREST_RATE',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: '特色产业：有关政策明确支持的产业、区域、企业、项目，或辖区内有上市公司总部或者开设工厂',
      dictType: 'number-10',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '过往合作履约情况',
      quotaName: '过往合作履约情况',
      dictType: 'FCT_LIMIT_PREVIOUS',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '负面舆情',
      quotaName: '负面舆情：区域内出现国有发债主体的债券违约事件或者频繁的非标融资、银行借款等债务逾期风险事件',
      dictType: 'FCT_LIMIT_SENTIMENT',
      numericalValue: '',
      remark: '',
    },
  ],
});

const title = computed(() => {
  return baseFormInfo.id ? '编辑' : '新增';
});

const areaInfoRef = ref();

const handleSave = async (isSubmit = false) => {
  try {
    const isValid = await areaInfoRef.value.validateForm();
    if (isValid) {
      const TableData = areaInfoRef.value.gridApi.grid?.getTableData();
      baseFormInfo.limitRuleList = TableData.fullData;
      const params = cloneDeep(baseFormInfo);

      if (!params.districtName) {
        params.cityCode = params.districtCode;
      }
      if (isSubmit) {
        const { processDefinitionKey, startUserSelectAssignees } = await startWorkflow();
        params.processDefinitionKey = processDefinitionKey;
        params.startUserSelectAssignees = startUserSelectAssignees;
        params.isSubmit = true;
      }
      emit('ok', params);
    }
  } catch (error) {
    console.error(`${isSubmit ? '提交' : '保存'}失败:`, error);
  } finally {
    changeOkLoading(false);
  }
};

const save = async () => {
  handleSave(false);
};

const submit = async () => {
  handleSave(true);
};

const isAgainCompute = ref(false);

// 添加初始化方法
const init = async (data: areaComputeVo) => {
  isAgainCompute.value = false;
  await initWorkflow({ formKey: 'fct_region_limit', businessKey: data.id });
  if (data && Object.keys(data).length > 0) {
    if (data.status === 'EFFECTIVE') {
      isAgainCompute.value = true;
    }
    if (areaInfoRef.value && data.limitRuleList.length > 0) {
      data.limitRuleList = data.limitRuleList.map((value, index) => {
        return {
          ...value,
          dictType: baseFormInfo.limitRuleList[index].dictType,
        };
      });
      areaInfoRef.value.gridApi.grid?.reloadData(data.limitRuleList);
    }
    Object.assign(baseFormInfo, cloneDeep(data));
  } else {
    Object.assign(baseFormInfo, {
      adjustSupportingFileId: 0,
      budgetAmount: undefined,
      companyRating: '',
      cityCode: '',
      cityName: '',
      creditAmount: 0,
      province: '江西省',
      creditRatio: 0,
      creditScore: 0,
      districtCode: '',
      isSubmit: false,
      districtName: '',
      id: undefined,
      limitRuleList: [
        {
          quotaCategory: '区域情况',
          quotaName: '人口（万人）',
          dictType: 'number',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '区域情况',
          quotaName: '财政平衡率（%）',
          dictType: 'number',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '区域情况',
          quotaName: 'GDP增速（%）',
          dictType: 'number',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '区域情况',
          quotaName: '财政实力（%）',
          dictType: 'number',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '区域情况',
          quotaName: '区域利差',
          dictType: 'FCT_LIMIT_INTEREST_RATE',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '区域情况',
          quotaName: '特色产业：有关政策明确支持的产业、区域、企业、项目，或辖区内有上市公司总部或者开设工厂',
          dictType: 'number-10',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '过往合作履约情况',
          quotaName: '过往合作履约情况',
          dictType: 'FCT_LIMIT_PREVIOUS',
          numericalValue: '',
          remark: '',
        },
        {
          quotaCategory: '负面舆情',
          quotaName: '负面舆情：区域内出现国有发债主体的债券违约事件或者频繁的非标融资、银行借款等债务逾期风险事件',
          dictType: 'FCT_LIMIT_SENTIMENT',
          numericalValue: '',
          remark: '',
        },
      ],
    });
  }
};

const [registerPopup, { changeOkLoading }] = usePopupInner((data) => init(data));
</script>

<template>
  <BasicPopup v-bind="$attrs" :show-ok-btn="!isAgainCompute" :title="title" @register="registerPopup" @ok="save">
    <template #centerToolbar>
      <Button class="ml-2" type="primary" @click="submit">提交</Button>
    </template>
    <AreaInfo ref="areaInfoRef" :is-again-compute="isAgainCompute" v-model="baseFormInfo" />
    <template>
      <WorkflowPreviewModal />
    </template>
  </BasicPopup>
</template>

<style scoped>
.text-with-newline {
  white-space: pre-line;

  /* 或 pre 或 pre-wrap */
}
</style>
