<script setup lang="ts">
import type { areaComputeVo } from '#/api';

import { reactive, ref } from 'vue';

import { prompt } from '@vben/common-ui';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { cloneDeep } from '@vben/utils';

import { message, Textarea } from 'ant-design-vue';

import { editAreaApi } from '#/api';
import { useWorkflowBase } from '#/composables/useWorkflowBase';

import AreaInfoDetail from '../components/areaInfo-detail.vue';

const emit = defineEmits(['ok', 'register']);
const {
  startWorkflow,
  WorkflowPreviewModal,
  useOperationButton,
  useViewButton,
  initWorkflow,
  isProcessInstance,
  isWorkflow,
  isRunningTask,
  isWorkflowLoading,
} = useWorkflowBase();

const pageType = ref('detail');
const ViewButton = useViewButton();
const baseFormInfo = reactive<areaComputeVo>({
  adjustSupportingFileId: 0,
  budgetAmount: 0,
  cityCode: '',
  cityName: '',
  creditAmount: 0,
  creditRatio: 0,
  creditScore: 0,
  districtCode: '',
  isSubmit: false,
  districtName: '',
  id: 0,
  limitRuleList: [
    {
      quotaCategory: '区域情况',
      quotaName: '人口（万人）',
      dictType: 'input',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: '财政平衡率（%）',
      dictType: 'input',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: 'GDP增速（%）',
      dictType: 'input',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: '财政实力（%）',
      dictType: 'input',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: '区域利差',
      dictType: 'FCT_LIMIT_INTEREST_RATE',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: '特色产业：有关政策明确支持的产业、区域、企业、项目，或辖区内有上市公司总部或者开设工厂',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '过往合作履约情况',
      quotaName: '过往合作履约情况',
      dictType: 'FCT_LIMIT_PREVIOUS',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '负面舆情',
      quotaName: '负面舆情：区域内出现国有发债主体的债券违约事件或者频繁的非标融资、银行借款等债务逾期风险事件',
      dictType: 'FCT_LIMIT_SENTIMENT',
      numericalValue: '',
      remark: '',
    },
  ],
});
const OperationButton = useOperationButton();
const areaInfoRef = ref();
// 添加初始化方法
const init = async (data: areaComputeVo) => {
  pageType.value = data.pageType ?? 'detail';
  if (data && Object.keys(data).length > 0) {
    await initWorkflow({ formKey: 'fct_region_limit', businessKey: data.id });
    if (areaInfoRef.value && data.limitRuleList.length > 0) {
      data.limitRuleList = data.limitRuleList.map((value, index) => {
        return {
          ...value,
          dictType: baseFormInfo.limitRuleList[index].dictType,
        };
      });
      areaInfoRef.value.gridApi.grid?.reloadData(data.limitRuleList);
    }
    Object.assign(baseFormInfo, cloneDeep(data));
  }
};

const state = reactive({
  loading: false,
});
const audit = async (status: number) => {
  if (status === 1) {
    const { processDefinitionKey, startUserSelectAssignees } = await startWorkflow();
    if (processDefinitionKey) {
      state.loading = true;
      try {
        await editAreaApi({
          ...baseFormInfo,
          isPass: status,
          processDefinitionKey,
          startUserSelectAssignees,
        });
        message.success($t('base.resSuccess'));
        emit('ok');
        closePopup();
      } finally {
        state.loading = false;
      }
      return false;
    }
  }
  prompt({
    content: '请输入审核意见：',
    title: '审核意见',
    component: Textarea,
    modelPropName: 'value',
    async beforeClose(scope) {
      if (!scope.isConfirm) return;
      if (!scope.value) {
        message.warning('请输入审核意见');
        return false;
      }
      state.loading = true;
      try {
        await editAreaApi({
          ...baseFormInfo,
          isPass: status,
          remark: scope.value,
        });
      } finally {
        state.loading = false;
      }
      return true;
    },
  }).then(() => {
    message.success($t('base.resSuccess'));
    emit('ok');
    closePopup();
  });
};
const workflowSuccess = () => {
  emit('ok');
  closePopup();
};
const [registerPopup, { closePopup }] = usePopupInner((data) => init(data));
</script>

<template>
  <BasicPopup v-bind="$attrs" title="详情" @register="registerPopup">
    <template #insertToolbar>
      <div v-if="pageType === 'audit'" v-loading="isWorkflowLoading">
        <OperationButton v-if="isProcessInstance" @success="workflowSuccess" />
        <a-space v-else>
          <a-button :loading="state.loading" type="primary" @click="audit(1)">通过</a-button>
          <a-button :loading="state.loading" type="primary" danger @click="audit(0)">驳回</a-button>
        </a-space>
      </div>
      <ViewButton v-if="isWorkflow && !isRunningTask && pageType === 'detail'" />
    </template>
    <AreaInfoDetail ref="areaInfoRef" v-model="baseFormInfo" />
    <template v-if="pageType === 'audit'">
      <WorkflowPreviewModal />
    </template>
  </BasicPopup>
</template>

<style scoped>
.text-with-newline {
  white-space: pre-line;

  /* 或 pre 或 pre-wrap */
}
</style>
