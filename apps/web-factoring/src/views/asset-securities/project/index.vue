<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { AbsProjectInfo } from '#/api';

import { useModalUrl } from '@vben/base-ui';
import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { message } from 'ant-design-vue';
import { Modal as AntdModal } from 'ant-design-vue/es/components';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { delAbsProjectApi, getAbsProjectPageListApi } from '#/api';

import ProjectDetail from './project-detail.vue';
import ProjectEdit from './project-edit.vue';

const dictStore = useDictStore();

const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'projectName',
      label: 'ABS项目名称',
    },
    {
      component: 'Input',
      fieldName: 'planManager',
      label: '计划管理人',
    },
    {
      component: 'Input',
      fieldName: 'guaranteeName',
      label: '担保人',
    },
    {
      component: 'Select',
      fieldName: 'statusList',
      label: '操作状态',
      componentProps: {
        options: dictStore.getDictList('FCT_STATUS'),
      },
    },
    {
      component: 'Select',
      fieldName: 'reviewStatusList',
      label: '审批状态',
      componentProps: {
        options: dictStore.getDictList('REVIEW_STATUS'),
      },
    },
  ],
  commonConfig: {
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
  },
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'projectName', title: 'ABS项目名称', minWidth: 200 },
    { field: 'originalBeneficiaryName', title: '原始受益人', minWidth: 120 },
    { field: 'planManager', title: '计划管理人', minWidth: 120 },
    { field: 'guaranteeName', title: '担保人', minWidth: 120 },
    { field: 'financeTermYear', title: '融资/发行期限（年）', minWidth: 200 },
    { field: 'declareFinancingScale', title: '融资规模（元）' },
    {
      field: 'productLayer',
      title: '产品分层',
      minWidth: 120,
      cellRender: { name: 'CellStatus', props: { code: 'FCT_ABS_PROJECT_PRODUCT_LAYER' } },
    },
    { field: 'declarationPlace', title: '申报场所', minWidth: 200 },
    {
      field: 'status',
      title: '操作状态',
      cellRender: { name: 'CellStatus', props: { code: 'FCT_STATUS' } },
    },
    {
      field: 'reviewStatus',
      title: '审批状态',
      cellRender: { name: 'CellStatus', props: { code: 'REVIEW_STATUS' } },
    },
    { field: 'action', title: '操作', fixed: 'right', width: 140, slots: { default: 'action' } },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getAbsProjectPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  checkboxConfig: {
    showHeader: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const add = () => {
  openFormPopup(true, {});
};
const edit = (row: AbsProjectInfo) => {
  openFormPopup(true, row);
};
const editSuccess = () => {
  gridApi.reload();
};
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
const viewDetail = (row: AbsProjectInfo) => {
  openDetailPopup(true, row);
};
const del = (row: AbsProjectInfo) => {
  const id = row.id;
  AntdModal.confirm({
    title: '确认删除',
    content: '此操作将删除该ABS项目，是否继续？',
    async onOk() {
      await delAbsProjectApi(id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};

// 使用组合式函数处理URL参数自动打开弹层
useModalUrl({
  handlers: {
    // 编辑弹层
    edit: (params) => {
      const data: AbsProjectInfo = {
        id: params.id,
      };
      edit(data);
    },

    // 详情弹层
    detail: (params) => {
      const data: AbsProjectInfo = {
        id: params.id,
      };
      viewDetail(data);
    },

    // 审核弹层
    audit: (params) => {
      const data: AbsProjectInfo = {
        id: params.id,
        pageType: 'audit',
      };
      audit(data);
    },
  },
});
const audit = (row: AbsProjectInfo) => {
  const data = { ...row, pageType: 'audit' };
  openDetailPopup(true, data);
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-space>
          <a-button class="mr-2" type="primary" @click="add">
            {{ $t('base.add') }}
          </a-button>
        </a-space>
      </template>
      <template #action="{ row }">
        <a-space>
          <a-typography-link v-if="['SUBMIT'].includes(row.status)" @click="edit(row)">
            {{ $t('base.edit') }}
          </a-typography-link>
          <a-typography-link @click="viewDetail(row)">{{ $t('base.detail') }}</a-typography-link>
          <a-typography-link type="danger" v-if="['SUBMIT'].includes(row.status)" @click="del(row)">
            {{ $t('base.del') }}
          </a-typography-link>
        </a-space>
      </template>
    </Grid>
    <ProjectEdit @register="registerForm" @ok="editSuccess" />
    <ProjectDetail @register="registerDetail" @ok="editSuccess" />
  </Page>
</template>

<style></style>
