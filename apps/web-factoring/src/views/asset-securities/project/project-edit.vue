<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { AbsProjectInfo } from '#/api';

import { reactive, ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, COL_SPAN_PROP, FORM_PROP, FULL_FORM_ITEM_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';

import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash-es';

import { BaseAttachmentList } from '#/adapter/base-ui';
import {
  addAbsProjectApi,
  editAbsProjectApi,
  getCompanyDetailByCodeApi,
  getUserListApi,
  infoAbsProjectApi,
} from '#/api';

const emit = defineEmits(['ok', 'register']);
const projectInfo = ref<AbsProjectInfo>({});
// 验证规则
const rules: Record<string, Rule[]> = {
  projectName: [{ required: true, message: '请输入ABS项目名称', trigger: 'blur' }],
  projectManager: [{ required: true, message: '请输入项目负责人', trigger: 'change' }],
  originalBeneficiaryName: [{ required: true, message: '请输入原始受益人', trigger: 'blur' }],
  guaranteeName: [{ required: true, message: '请输入担保人', trigger: 'blur' }],
  planManager: [{ required: true, message: '请输入计划管理人', trigger: 'blur' }],
  returnMethod: [{ required: true, message: '请选择还本方式', trigger: 'change' }],
  returnInterestMethod: [{ required: true, message: '请选择还息方式', trigger: 'change' }],
};

const init = async (data: AbsProjectInfo) => {
  projectInfo.value = {};
  if (data.id) {
    const info = await infoAbsProjectApi({ id: data.id as number });
    if (info.establishDate) info.establishDate = dayjs(info.establishDate).valueOf().toString();
    projectInfo.value = info;
  } else {
    projectInfo.value.originalBeneficiaryCode = '91361200MAC5TQUL5M';
    const companyDetail = await getCompanyDetailByCodeApi({ code: projectInfo.value.originalBeneficiaryCode });
    projectInfo.value.originalBeneficiaryName = companyDetail.companyName;
    projectInfo.value.establishDate = dayjs().format('x');
  }
};
const userOptions = ref<{ userId: number; userName: string }[]>([]);

const loadUserOptions = async () => {
  userOptions.value = await getUserListApi();
};

loadUserOptions();

const filterOption = (input: string, option: any) => {
  return option.realName.toLowerCase().includes(input.toLowerCase());
};

// 获取人员名称
const getUserLabel = (userId: string): string => {
  if (!userId) return '';

  // 从缓存中查找
  const user = userOptions.value.find((item) => item.id === userId);

  return user?.realName || '';
};
const loading = reactive({
  submit: false,
});
const dictStore = useDictStore();
const FormRef = ref();
const save = async (type: string) => {
  await FormRef.value.validate();
  let api = addAbsProjectApi;
  if (projectInfo.value.id) {
    api = editAbsProjectApi;
  }
  loading.submit = true;
  const formData = cloneDeep(projectInfo.value);
  formData.projectManagerName = getUserLabel(formData.projectManager);
  formData.establishDate = Number(formData.establishDate);
  if (type === 'submit') {
    formData.isSubmit = true;
    formData.processDefinitionKey = 'fct_abs_project';
  }
  try {
    const res = await api(formData);
    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
  } finally {
    loading.submit = false;
  }
};
const formProp = { ...FORM_PROP, labelCol: { span: 8 }, wrapperCol: { span: 16 } };
const colSpan = COL_SPAN_PROP;
const fullProp = { ...FULL_FORM_ITEM_PROP, labelCol: { span: 4 }, wrapperCol: { span: 20 } };
const [registerPopup, { closePopup }] = usePopupInner(init);
</script>

<template>
  <BasicPopup v-bind="$attrs" title="ABS项目" @register="registerPopup">
    <template #insertToolbar>
      <a-space>
        <a-button type="primary" :loading="loading.submit" @click="save('save')">保存</a-button>
        <a-button type="primary" :loading="loading.submit" @click="save('submit')">提交</a-button>
      </a-space>
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-form ref="FormRef" class="mt-5" :model="projectInfo" :rules="rules" v-bind="formProp">
        <!-- 基本信息 -->
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="ABS项目名称" name="projectName">
              <a-input v-model:value="projectInfo.projectName" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="ABS项目编号" name="projectCode">
              <a-input v-model:value="projectInfo.projectCode" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="立项日期" name="establishDate">
              <a-date-picker v-model:value="projectInfo.establishDate" value-format="x" class="w-full" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="项目负责人" name="projectManager">
              <a-select
                v-model:value="projectInfo.projectManager"
                show-search
                :options="userOptions"
                :field-names="{ label: 'realName', value: 'id' }"
                :filter-option="filterOption"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="ABS项目简介" name="absProjectSummary" v-bind="fullProp">
              <a-textarea
                v-model:value="projectInfo.absProjectSummary"
                :rows="4"
                placeholder="对项目的总体情况进行简要描述，包括项目背景、目的、预期效果等。"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 参与主体 -->
        <BasicCaption content="参与主体" />
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="原始受益人" name="originalBeneficiaryName">
              <a-input v-model:value="projectInfo.originalBeneficiaryName" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="担保人" name="guaranteeName">
              <a-input v-model:value="projectInfo.guaranteeName" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="计划管理人" name="planManager">
              <a-input v-model:value="projectInfo.planManager" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="托管银行" name="custodianBank">
              <a-input v-model:value="projectInfo.custodianBank" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="评级机构" name="ratingAgency">
              <a-input v-model:value="projectInfo.ratingAgency" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="律师事务所" name="lawFirm">
              <a-input v-model:value="projectInfo.lawFirm" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="会计师事务所" name="accountingFirm">
              <a-input v-model:value="projectInfo.accountingFirm" />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 应收账款ABS产品要素 -->
        <BasicCaption content="应收账款ABS产品要素" />
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="基础资产" name="baseAsset">
              <a-input
                v-model:value="projectInfo.baseAsset"
                placeholder="原始权益人依据基础交易合同对债务人享有的应收账款债权及附属担保权益（如有）"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="融资期限（年）" name="financeTermYear">
              <a-input v-model:value="projectInfo.financeTermYear" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="申报融资规模（元）" name="declareFinancingScale">
              <a-input-number v-model:value="projectInfo.declareFinancingScale" :controls="false" class="w-full" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="产品分层" name="productLayer">
              <a-select
                v-model:value="projectInfo.productLayer"
                :options="dictStore.getDictList('FCT_ABS_PROJECT_PRODUCT_LAYER')"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="还本方式" name="returnMethod">
              <a-select
                v-model:value="projectInfo.returnMethod"
                :options="dictStore.getDictList('FCT_ABS_PROJECT_RETURN_METHOD')"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="还息方式" name="returnInterestMethod">
              <a-select
                v-model:value="projectInfo.returnInterestMethod"
                :options="dictStore.getDictList('FCT_ABS_PROJECT_INTEREST_METHOD')"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="申报场所" name="declarationPlace">
              <a-input v-model:value="projectInfo.declarationPlace" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="增信措施" name="creditEnhancementMethod">
              <a-input v-model:value="projectInfo.creditEnhancementMethod" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="募集资金用途" name="declarationPurpose" v-bind="fullProp">
              <a-textarea
                v-model:value="projectInfo.declarationPurpose"
                :rows="4"
                placeholder="请输入募集资金的具体用途"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 项目合规性审查 -->
        <BasicCaption content="项目合规性审查" />
        <a-row class="mt-5">
          <a-col :span="24">
            <a-form-item label="法律法规依据" name="legalBasis" v-bind="fullProp">
              <a-textarea
                v-model:value="projectInfo.legalBasis"
                :rows="4"
                placeholder="说明项目所依据的相关法律法规，如《合同法》《证券法》等"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="审批备案情况" name="approvalFilingSummary" v-bind="fullProp">
              <a-textarea
                v-model:value="projectInfo.approvalFilingSummary"
                :rows="4"
                placeholder="项目是否已获得相关监管部门的审批或备案，以及文号、时间等信息"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <BasicCaption content="项目风险评审" />
        <a-row class="mt-5">
          <a-col :span="24">
            <a-form-item label="信用风险评估" name="creditRiskSummary" v-bind="fullProp">
              <a-textarea
                v-model:value="projectInfo.creditRiskSummary"
                :rows="4"
                placeholder="对债务人的信用状况进行评估的结果，包括信用评级、违约概率等相关指标。"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="市场风险评估" name="marketRiskSummary" v-bind="fullProp">
              <a-textarea
                v-model:value="projectInfo.marketRiskSummary"
                :rows="4"
                placeholder="分析市场因素（如利率波动、行业竞争等）对基础资产现金流和证券收益的影响程度。"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="操作风险评估" name="operationalRiskSummary" v-bind="fullProp">
              <a-textarea
                v-model:value="projectInfo.operationalRiskSummary"
                :rows="4"
                placeholder="对项目在运营管理过程中可能面临的操作风险（如合同管理风险、资金划转风险等）进行评估的结果和应对措施。"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <BaseAttachmentList
          v-model="projectInfo.attachmentList"
          :business-id="projectInfo.id"
          business-type="FCT_ABS_PROJECT"
          edit-mode
        />
      </a-form>
    </div>
  </BasicPopup>
</template>
