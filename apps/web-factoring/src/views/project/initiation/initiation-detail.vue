<script setup lang="ts">
import type { InitiationInfo } from '#/api';

import { ref } from 'vue';

import { BASE_PAGE_CLASS_NAME } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';

import { getInitiationInfoApi } from '#/api';
import { useWorkflowBase } from '#/composables/useWorkflowBase';
import ComprehensiveDetail from '#/views/project/initiation/components/comprehensive-detail.vue';
import SingleDetail from '#/views/project/initiation/components/single-detail.vue';

const emit = defineEmits(['ok', 'register']);
const {
  WorkflowPreviewModal,
  useOperationButton,
  useViewButton,
  initWorkflow,
  isWorkflow,
  isRunningTask,
  isWorkflowLoading,
} = useWorkflowBase();

const OperationButton = useOperationButton();
const ViewButton = useViewButton();
const pageType = ref('detail');
const initiationForm = ref<InitiationInfo>({});
const init = async (data: InitiationInfo) => {
  pageType.value = data.pageType ?? 'detail';
  await initWorkflow({ formKey: 'fct_project_proposal', businessKey: data.id });
  const info = data?.id ? await getInitiationInfoApi(data.id as number) : data;
  info.businessTypeFile = 'FCT_PROJECT_PROPOSAL';
  initiationForm.value = info;
};
const workflowSuccess = () => {
  emit('ok');
  closePopup();
};
const [registerPopup, { closePopup }] = usePopupInner(init);
</script>

<template>
  <BasicPopup v-bind="$attrs" title="项目立项" @register="registerPopup">
    <template #insertToolbar>
      <div v-if="pageType === 'audit'" v-loading="isWorkflowLoading">
        <OperationButton @success="workflowSuccess" />
      </div>
      <ViewButton v-if="isWorkflow && !isRunningTask && pageType === 'detail'" />
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <ComprehensiveDetail v-show="initiationForm.projectType === 'comprehensive'" :form="initiationForm" />
      <SingleDetail v-show="initiationForm.projectType === 'single'" :form="initiationForm" />
    </div>
    <template v-if="pageType === 'audit'">
      <WorkflowPreviewModal />
    </template>
  </BasicPopup>
</template>

<style></style>
