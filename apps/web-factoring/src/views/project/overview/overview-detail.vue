<script setup lang="ts">
import type { ContractInfo, OverviewInfo, PricingInfo } from '#/api';

import { nextTick, ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { formatDate } from '@vben/utils';

import { cloneDeep, omit } from 'lodash-es';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { getOverviewContractListApi, getOverviewInfoApi, getPricingInfoApi, getProjectContractDetail } from '#/api';
import { ContractList } from '#/components';
import { useWorkflowBase } from '#/composables/useWorkflowBase';
import DebtServiceDetail from '#/views/project/components/debt-service-detail.vue';
import RepaymentCalculationDetail from '#/views/project/components/repayment-calculation-detail.vue';
import RepaymentCalculationHistory from '#/views/project/components/repayment-calculation-history.vue';
import ComprehensiveDetail from '#/views/project/initiation/components/comprehensive-detail.vue';
import SingleDetail from '#/views/project/initiation/components/single-detail.vue';
import ChangeRecord from '#/views/project/overview/components/change-record.vue';
import CreditApplyInfo from '#/views/project/overview/components/credit-apply-info.vue';
import CreditPricingInfo from '#/views/project/overview/components/credit-pricing-info.vue';
import PaymentApplyInfo from '#/views/project/overview/components/payment-apply-info.vue';
import PaymentRecordInfo from '#/views/project/overview/components/payment-record-info.vue';
import RepaymentChangeInfo from '#/views/project/overview/components/repayment-change-info.vue';
import RepaymentPlanInfo from '#/views/project/overview/components/repayment-plan-info.vue';
import RepaymentRecordInfo from '#/views/project/overview/components/repayment-record-info.vue';
import ReviewInfo from '#/views/project/overview/components/review-info.vue';
import SettlementRecordInfo from '#/views/project/overview/components/settlement-record-info.vue';
import BaseDetail from '#/views/project/pricing/components/base-detail.vue';
import PricingScheme from '#/views/project/pricing/components/pricing-scheme.vue';

const emit = defineEmits(['ok', 'register']);
const {
  WorkflowPreviewModal,
  useOperationButton,
  useViewButton,
  initWorkflow,
  isWorkflow,
  isRunningTask,
  isWorkflowLoading,
} = useWorkflowBase();

const OperationButton = useOperationButton();
const ViewButton = useViewButton();
const pageType = ref('detail');
const initiationForm = ref<OverviewInfo>({});
const ChangeRecordRef = ref();
const init = async (data: OverviewInfo) => {
  pageType.value = data.pageType ?? 'detail';
  await initWorkflow({ formKey: 'fct_project', businessKey: data.id });
  activeKey.value = 'initiation';
  const info = data?.id ? await getOverviewInfoApi(data.id as number) : data;
  info.businessTypeFile = 'FCT_PROJECT';
  initiationForm.value = info;
  if (initiationForm.value.pricingId) {
    await getPricingInfo(initiationForm.value);
  }
};
const contractList = ref();
const contractForm = ref<ContractInfo>({});
const CreditPricingInfoRef = ref();
const CreditApplyInfoRef = ref();
const PaymentApplyInfoRef = ref();
const PaymentRecordInfoRef = ref();
const RepaymentPlanInfoRef = ref();
const RepaymentChangeInfoRef = ref();
const RepaymentRecordInfoRef = ref();
const SettlementRecordInfoRef = ref();
const changeTab = (key: string) => {
  if (key === 'creditPricingInfo') {
    nextTick(() => {
      CreditPricingInfoRef.value.init(initiationForm.value);
    });
  }
  if (key === 'creditApplyInfo') {
    nextTick(() => {
      CreditApplyInfoRef.value.init(initiationForm.value);
    });
  }
  if (key === 'paymentApplyInfo') {
    nextTick(() => {
      PaymentApplyInfoRef.value.init(initiationForm.value);
    });
  }
  if (key === 'paymentRecordInfo') {
    nextTick(() => {
      PaymentRecordInfoRef.value.init(initiationForm.value);
    });
  }
  if (key === 'repaymentPlanInfo') {
    nextTick(() => {
      RepaymentPlanInfoRef.value.init(initiationForm.value);
    });
  }
  if (key === 'repaymentChangeInfo') {
    nextTick(() => {
      RepaymentChangeInfoRef.value.init(initiationForm.value);
    });
  }
  if (key === 'repaymentRecordInfo') {
    nextTick(() => {
      RepaymentRecordInfoRef.value.init(initiationForm.value);
    });
  }
  if (key === 'settlementRecordInfo') {
    nextTick(() => {
      SettlementRecordInfoRef.value.init(initiationForm.value);
    });
  }
  if (key === 'changeRecord') {
    nextTick(() => {
      ChangeRecordRef.value.init(initiationForm.value);
    });
  }
  if (key === 'pricing' && pricingForm.value.projectType === 'single') {
    nextTick(() => {
      RepaymentCalculationHistoryRef.value.init(pricingForm.value);
    });
  }
  if (key === 'contract') {
    nextTick(async () => {
      const param = {
        bizType: 'FCT_PROJECT_CONTRACT',
        bizId: initiationForm.value.id,
      };
      const contractRes = await getOverviewContractListApi(param);
      contractList.value = contractRes.data;
      const detailRes = await getProjectContractDetail(initiationForm.value.id as number);
      contractForm.value = cloneDeep(detailRes) || {};
    });
  }
};
const pricingForm = ref<OverviewInfo>({});
const getPricingInfo = async (data: PricingInfo) => {
  let info = data.id ? await getPricingInfoApi(data.pricingId as number) : data;
  if (info.projectType === 'single') {
    const calculation = omit(
      info.calculation,
      'id',
      'targetCompanyName',
      'targetCompanyCode',
      'projectCode',
      'projectName',
    );
    info = {
      ...info,
      ...calculation,
    };
  }
  pricingForm.value = info;
};
const RepaymentCalculationHistoryRef = ref();
const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: {
    width: '220px',
    justifyContent: 'flex-end',
  },
};
const activeKey = ref('initiation');
const workflowSuccess = () => {
  emit('ok');
  closePopup();
};
const [registerPopup, { closePopup }] = usePopupInner(init);

const handleClose = () => {
  contractForm.value = {};
};
</script>

<template>
  <BasicPopup v-bind="$attrs" :show-ok-btn="false" title="项目总览" @register="registerPopup" @close="handleClose">
    <template #insertToolbar>
      <div v-if="pageType === 'audit'" v-loading="isWorkflowLoading">
        <OperationButton @success="workflowSuccess" />
      </div>
      <ViewButton v-if="isWorkflow && !isRunningTask && pageType === 'detail'" />
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-tabs v-model:active-key="activeKey" @change="changeTab">
        <a-tab-pane key="initiation" tab="立项信息">
          <ComprehensiveDetail v-show="initiationForm.projectType === 'comprehensive'" :form="initiationForm" />
          <SingleDetail v-show="initiationForm.projectType === 'single'" :form="initiationForm" />
        </a-tab-pane>
        <a-tab-pane key="pricing" tab="项目定价" v-if="initiationForm.pricingId">
          <BaseDetail :pricing-form="pricingForm" :descriptions-prop="descriptionsProp" />
          <PricingScheme :pricing-form="pricingForm" :descriptions-prop="descriptionsProp" />
          <div v-show="pricingForm.projectType === 'single'">
            <DebtServiceDetail :debt-service-form="pricingForm" :descriptions-prop="descriptionsProp" />
            <RepaymentCalculationDetail
              :calculation-form="pricingForm"
              :descriptions-prop="descriptionsProp"
              calculation-type="Pricing"
            />
            <RepaymentCalculationHistory ref="RepaymentCalculationHistoryRef" calculation-type="Pricing" />
          </div>
          <BaseAttachmentList :business-id="pricingForm.id" business-type="FCT_PROJECT_PRICING" />
        </a-tab-pane>
        <a-tab-pane key="reviewInfo" tab="评审信息">
          <ReviewInfo :info-form="initiationForm" v-if="initiationForm.meetingReviewId" />
        </a-tab-pane>
        <a-tab-pane key="contract" tab="合同信息">
          <div :class="BASE_PAGE_CLASS_NAME">
            <BasicCaption content="总经办决策" v-if="initiationForm.projectType === 'single'" />
            <a-descriptions class="mt-4" v-bind="descriptionsProp" v-if="initiationForm.projectType === 'single'">
              <a-descriptions-item label="决策日期">
                {{ formatDate(contractForm.decisionDate) }}
              </a-descriptions-item>
              <a-descriptions-item label="授信期限（个月）">
                {{ contractForm.creditTerm }}
              </a-descriptions-item>
              <a-descriptions-item label="授信到期日">
                {{ formatDate(contractForm.creditExpireDate) }}
              </a-descriptions-item>
              <a-descriptions-item label="授信额度（元）">
                {{ contractForm.creditAmount }}
              </a-descriptions-item>
            </a-descriptions>
            <BasicCaption content="主合同信息" />
            <a-descriptions class="mt-4" v-bind="descriptionsProp">
              <a-descriptions-item label="保理合同名称">
                {{ contractForm.contractName }}
              </a-descriptions-item>
              <a-descriptions-item label="保理合同编号">
                {{ contractForm.contractCode }}
              </a-descriptions-item>
              <a-descriptions-item label="签署日期">
                {{ formatDate(contractForm.signDate) }}
              </a-descriptions-item>
              <a-descriptions-item label="合同日期范围">
                {{ formatDate(contractForm.contractStartDate) }} ~ {{ formatDate(contractForm.contractEndDate) }}
              </a-descriptions-item>
              <a-descriptions-item label="合同期限（个月）">
                {{ contractForm.contractTerm }}
              </a-descriptions-item>
              <a-descriptions-item label="合同金额（元）">
                {{ contractForm.contractAmount }}
              </a-descriptions-item>
            </a-descriptions>
            <ContractList
              v-model="contractList"
              :business-id="initiationForm.id"
              business-type="FCT_PROJECT_CONTRACT"
            />
          </div>
        </a-tab-pane>
        <a-tab-pane key="creditPricingInfo" tab="用信定价" v-if="initiationForm.projectType === 'comprehensive'">
          <CreditPricingInfo ref="CreditPricingInfoRef" />
        </a-tab-pane>
        <a-tab-pane key="creditApplyInfo" tab="用信申请" v-if="initiationForm.projectType === 'comprehensive'">
          <CreditApplyInfo ref="CreditApplyInfoRef" />
        </a-tab-pane>
        <a-tab-pane key="paymentApplyInfo" tab="付款申请">
          <PaymentApplyInfo ref="PaymentApplyInfoRef" />
        </a-tab-pane>
        <a-tab-pane key="paymentRecordInfo" tab="付款记录">
          <PaymentRecordInfo ref="PaymentRecordInfoRef" />
        </a-tab-pane>
        <a-tab-pane key="repaymentPlanInfo" tab="还款计划">
          <RepaymentPlanInfo ref="RepaymentPlanInfoRef" />
        </a-tab-pane>
        <a-tab-pane key="repaymentChangeInfo" tab="还款变更">
          <RepaymentChangeInfo ref="RepaymentChangeInfoRef" />
        </a-tab-pane>
        <a-tab-pane key="repaymentRecordInfo" tab="还款记录">
          <RepaymentRecordInfo ref="RepaymentRecordInfoRef" />
        </a-tab-pane>
        <a-tab-pane key="settlementRecordInfo" tab="结清记录">
          <SettlementRecordInfo ref="SettlementRecordInfoRef" />
        </a-tab-pane>
        <a-tab-pane key="changeRecord" tab="变更记录">
          <ChangeRecord ref="ChangeRecordRef" />
        </a-tab-pane>
      </a-tabs>
    </div>
    <template v-if="pageType === 'audit'">
      <WorkflowPreviewModal />
    </template>
  </BasicPopup>
</template>

<style></style>
