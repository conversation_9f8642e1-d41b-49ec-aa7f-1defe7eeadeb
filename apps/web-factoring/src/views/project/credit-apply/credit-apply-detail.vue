<script setup lang="ts">
import type { CreditApplyInfo } from '#/api';

import { ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';
import { formatDate } from '@vben/utils';

import { omit } from 'lodash-es';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { getCreditApplyInfoApi } from '#/api';
import { useWorkflowBase } from '#/composables/useWorkflowBase';
import AccountsReceivableDetail from '#/views/project/components/accounts-receivable-detail.vue';
import AccountsReceivablePoolDetail from '#/views/project/components/accounts-receivable-pool-detail.vue';
import LadderPenaltyDetail from '#/views/project/components/ladder-penalty-detail.vue';
import MortgageDetail from '#/views/project/components/mortgage-detail.vue';
import PledgeDetail from '#/views/project/components/pledge-detail.vue';
import RepaymentCalculationDetail from '#/views/project/components/repayment-calculation-detail.vue';

const emit = defineEmits(['ok', 'register']);
const {
  WorkflowPreviewModal,
  useOperationButton,
  useViewButton,
  initWorkflow,
  isWorkflow,
  isRunningTask,
  isWorkflowLoading,
} = useWorkflowBase();

const OperationButton = useOperationButton();
const ViewButton = useViewButton();
const pageType = ref('detail');
const dictStore = useDictStore();
const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: {
    width: '220px',
    justifyContent: 'flex-end',
  },
};
const init = async (data: CreditApplyInfo) => {
  pageType.value = data.pageType ?? 'detail';
  await initWorkflow({ formKey: 'fct_project_credit_apply', businessKey: data.id });
  let info = data.id ? await getCreditApplyInfoApi(data.id as number) : data;
  const calculation = omit(info.calculation, 'id');
  const contract = omit(info.contract, 'id');
  info = {
    ...info,
    ...calculation,
    ...contract,
  };
  creditApplyForm.value = info;
  creditApplyForm.value = processCompanyList(creditApplyForm.value.companyList);
};
// 处理关联企业数据的纯函数
const processCompanyList = (companyList: any) => {
  const processed = { ...creditApplyForm.value };

  // 提取担保企业
  const guarantee = companyList
    .filter((item: any) => item.projectCompanyType === 'guarantee')
    .map((item: any) => item.companyName || '');
  processed.guarantor = guarantee.join(',');

  // 提取债权人
  const creditor = companyList
    .filter((item: any) => item.projectCompanyType === 'creditor')
    .map((item: any) => item.companyName || '');
  processed.creditor = creditor.join(',');

  // 提取债务人
  const debtor = companyList
    .filter((item: any) => item.projectCompanyType === 'debtor')
    .map((item: any) => item.companyName || '');
  processed.debtor = debtor.join(',');

  return processed;
};
const workflowSuccess = () => {
  emit('ok');
  closePopup();
};
const [registerPopup, { closePopup }] = usePopupInner(init);

const creditApplyForm = ref<CreditApplyInfo>({});
</script>

<template>
  <BasicPopup title="用信申请详情" @register="registerPopup">
    <template #insertToolbar>
      <div v-if="pageType === 'audit'" v-loading="isWorkflowLoading">
        <OperationButton @success="workflowSuccess" />
      </div>
      <ViewButton v-if="isWorkflow && !isRunningTask && pageType === 'detail'" />
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions v-bind="descriptionsProp" class="mt-4">
        <a-descriptions-item label="关联综合授信项目">
          {{ creditApplyForm.projectName }}
        </a-descriptions-item>
        <a-descriptions-item label="关联用信定价">
          {{ creditApplyForm.projectPricingName }}
        </a-descriptions-item>
        <a-descriptions-item label="用信申请名称">
          {{ creditApplyForm.projectCreditApplyName }}
        </a-descriptions-item>
        <!--        <a-descriptions-item label="授信企业">-->
        <!--          {{ creditApplyForm.creditCompanyName }}-->
        <!--        </a-descriptions-item>-->
        <a-descriptions-item label="投向行业">
          {{ dictStore.formatter(creditApplyForm.targetIndustry, 'FCT_TARGET_INDUSTRY') }}
        </a-descriptions-item>
        <a-descriptions-item label="是否支持实体经济">
          {{ dictStore.formatter(creditApplyForm.isSupportRealEconomy, 'baseBooleanType') }}
        </a-descriptions-item>
        <a-descriptions-item label="是否为省重点产业">
          {{ dictStore.formatter(creditApplyForm.isProvincialKeyIndustry, 'baseBooleanType') }}
        </a-descriptions-item>
        <a-descriptions-item />
        <a-descriptions-item label="备注" :span="2">
          {{ creditApplyForm.remarks }}
        </a-descriptions-item>
      </a-descriptions>

      <BasicCaption content="授信批复方案" />
      <a-descriptions v-bind="descriptionsProp" class="mt-4">
        <a-descriptions-item label="授信金额（元）">
          {{ creditApplyForm.pricingCreditAmount }}
        </a-descriptions-item>
        <a-descriptions-item label="授信期限（月）">
          {{ creditApplyForm.pricingCreditTerm }}
        </a-descriptions-item>
        <a-descriptions-item label="授信费率（%/年）">
          {{ creditApplyForm.pricingCreditRate }}
        </a-descriptions-item>
        <a-descriptions-item label="授信方式">
          {{ dictStore.formatter(creditApplyForm.pricingCreditType, 'FCT_CREDIT_TYPE') }}
        </a-descriptions-item>
        <a-descriptions-item label="项目已投放金额（元）">
          {{ creditApplyForm.investedAmount }}
        </a-descriptions-item>
        <a-descriptions-item label="项目存量余额（元）">
          {{ creditApplyForm.stockBalance }}
        </a-descriptions-item>
      </a-descriptions>

      <BasicCaption content="业务类型" />
      <a-descriptions v-bind="descriptionsProp" class="mt-4">
        <a-descriptions-item label="保理类型">
          {{ dictStore.formatter(creditApplyForm.factoringType, 'FCT_FACTORING_TYPE') }}
        </a-descriptions-item>
        <a-descriptions-item label="支持操作模式">
          {{ dictStore.formatter(creditApplyForm.supportMode, 'FCT_SUPPORT_MODE') }}
        </a-descriptions-item>
        <a-descriptions-item label="支持保理方向">
          {{ dictStore.formatter(creditApplyForm.factoringDirection, 'FCT_FACTORING_DIRECTION') }}
        </a-descriptions-item>
        <a-descriptions-item label="追索权要求">
          {{ dictStore.formatter(creditApplyForm.recourseRequired, 'FCT_RECOURES_REQUIRED') }}
        </a-descriptions-item>
      </a-descriptions>
      <component
        :is="
          creditApplyForm.factoringType !== 'pool_factoring' ? AccountsReceivableDetail : AccountsReceivablePoolDetail
        "
        :info="creditApplyForm"
      />
      <template v-if="['refinance_factoring', 'lease_factoring'].includes(creditApplyForm.factoringType)">
        <BasicCaption content="原项目合同概况" />
        <a-descriptions v-bind="descriptionsProp" class="mt-4">
          <a-descriptions-item label="原合同编号">
            {{ creditApplyForm.contractCode }}
          </a-descriptions-item>
          <a-descriptions-item label="原合同签署日期">
            {{ formatDate(creditApplyForm.contractSignDate) }}
          </a-descriptions-item>
          <a-descriptions-item label="原合同到期日期">
            {{ formatDate(creditApplyForm.contractDueDate) }}
          </a-descriptions-item>
          <a-descriptions-item label="原合同金额（元）">
            {{ creditApplyForm.contractAmount }}
          </a-descriptions-item>
          <a-descriptions-item label="原合同已回收本金（元）">
            {{ creditApplyForm.contractRecycleAmount }}
          </a-descriptions-item>
          <a-descriptions-item label="剩余权益价值金额（元）">
            {{ creditApplyForm.contractRemainingAmount }}
          </a-descriptions-item>
          <template v-if="creditApplyForm.factoringType === 'refinance_factoring'">
            <a-descriptions-item label="保理商">
              {{ creditApplyForm.contractFactor }}
            </a-descriptions-item>
            <a-descriptions-item label="债权人">
              {{ creditApplyForm.contractCreditor }}
            </a-descriptions-item>
          </template>
          <template v-if="creditApplyForm.factoringType === 'lease_factoring'">
            <a-descriptions-item label="出租人">
              {{ creditApplyForm.contractLessor }}
            </a-descriptions-item>
            <a-descriptions-item label="承租人">
              {{ creditApplyForm.contractLessee }}
            </a-descriptions-item>
          </template>
          <a-descriptions-item label="底层资产">
            {{ creditApplyForm.contractAssets }}
          </a-descriptions-item>
          <a-descriptions-item label="拟转让应收账款概况">
            {{ creditApplyForm.contractTransferredDesc }}
          </a-descriptions-item>
        </a-descriptions>
      </template>
      <!-- 交易主体信息 -->
      <BasicCaption content="交易主体信息" />
      <a-descriptions v-bind="descriptionsProp" class="mt-4">
        <a-descriptions-item label="债权人">
          {{ creditApplyForm.creditor }}
        </a-descriptions-item>
        <a-descriptions-item label="担保人">
          {{ creditApplyForm.guarantor }}
        </a-descriptions-item>
        <a-descriptions-item label="债务人">
          {{ creditApplyForm.debtor }}
        </a-descriptions-item>
      </a-descriptions>
      <MortgageDetail :info="creditApplyForm" />
      <PledgeDetail :info="creditApplyForm" />
      <BasicCaption content="用信核心要素" />
      <a-descriptions v-bind="descriptionsProp" class="mt-4">
        <a-descriptions-item label="放款主体">
          {{ creditApplyForm.lenderCompanyName }}
        </a-descriptions-item>
        <a-descriptions-item label="用信申请人">
          {{ creditApplyForm.applyCompanyName }}
        </a-descriptions-item>
        <a-descriptions-item label="用信申请金额（元）">
          {{ creditApplyForm.applyAmount }}
        </a-descriptions-item>
        <a-descriptions-item label="融资比例（%）">
          {{ creditApplyForm.financingRatio }}
        </a-descriptions-item>
        <a-descriptions-item label="用信定价综合收益率（%/年）">
          {{ creditApplyForm.pricingXirrRate }}
        </a-descriptions-item>
        <a-descriptions-item label="预估融资到期日">
          {{ formatDate(creditApplyForm.expectedFinancingDueDate) }}
        </a-descriptions-item>
        <a-descriptions-item label="服务费金额（元）">
          {{ creditApplyForm.serviceFeeAmount }}
        </a-descriptions-item>
        <a-descriptions-item label="宽限期天数（日）">
          {{ creditApplyForm.gracePeriodDays }}
        </a-descriptions-item>
        <a-descriptions-item label="宽限期费率（%/年）">
          {{ creditApplyForm.gracePeriodRate }}
        </a-descriptions-item>
        <a-descriptions-item label="提用方式">
          {{ dictStore.formatter(creditApplyForm.drawingMethod, 'FCT_DRAWING_METHOD') }}
        </a-descriptions-item>
        <a-descriptions-item label="增信措施">
          {{ creditApplyForm.creditEnhancementDesc }}
        </a-descriptions-item>
        <a-descriptions-item label="第一还款来源">
          {{ creditApplyForm.firstRepaySource }}
        </a-descriptions-item>
        <a-descriptions-item label="补充还款来源">
          {{ creditApplyForm.supplementRepaySource }}
        </a-descriptions-item>
        <a-descriptions-item label="罚息类型">
          {{ dictStore.formatter(creditApplyForm.penaltyType, 'FCT_PENALTY_TYPE') }}
        </a-descriptions-item>
        <a-descriptions-item label="固定罚息利率（%）" v-if="creditApplyForm.penaltyType === 'fixed'">
          {{ creditApplyForm.penaltyInterestRate }}
        </a-descriptions-item>
      </a-descriptions>
      <LadderPenaltyDetail v-show="creditApplyForm.penaltyType === 'ladder'" :penalty-form="creditApplyForm" />
      <RepaymentCalculationDetail
        :calculation-form="creditApplyForm"
        :descriptions-prop="descriptionsProp"
        calculation-type="CreditApply"
      />
      <BaseAttachmentList :business-id="creditApplyForm.id" business-type="FCT_PROJECT_CREDIT_APPLY" />
      <BaseAttachmentList :business-id="creditApplyForm.id" business-type="FCT_PROJECT_CREDIT_APPLY_APPLICATION">
        <template #header>
          <BasicCaption content="用信申请报告" />
        </template>
      </BaseAttachmentList>
      <BaseAttachmentList :business-id="creditApplyForm.id" business-type="FCT_PROJECT_CREDIT_APPLY_RISK">
        <template #header>
          <BasicCaption content="风控复核报告" />
        </template>
      </BaseAttachmentList>
      <BaseAttachmentList :business-id="creditApplyForm.id" business-type="FCT_PROJECT_CREDIT_APPLY_OPERATION">
        <template #header>
          <BasicCaption content="运营复核报告" />
        </template>
      </BaseAttachmentList>
    </div>
    <template v-if="pageType === 'audit'">
      <WorkflowPreviewModal />
    </template>
  </BasicPopup>
</template>

<style></style>
