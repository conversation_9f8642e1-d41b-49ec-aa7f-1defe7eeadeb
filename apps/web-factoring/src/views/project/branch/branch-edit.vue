<script setup lang="ts">
import type { BranchInfo, ReviewInfo } from '#/api';

import { reactive, ref } from 'vue';

import { ApiComponent } from '@vben/common-ui';
import { BASE_PAGE_CLASS_NAME, COL_SPAN_PROP, FORM_PROP, FULL_FORM_ITEM_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';

import { message, Select } from 'ant-design-vue';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { addBranchApi, editBranchApi, getBranchInfoApi, getReviewMeetingListApi } from '#/api';

const emit = defineEmits(['ok', 'register']);
const init = async (data: BranchInfo) => {
  branchForm.value = {};
  branchForm.value = data.id ? await getBranchInfoApi(data.id as number) : data;
};
const [registerPopup, { closePopup }] = usePopupInner(init);
const FormRef = ref();
const branchForm = ref<BranchInfo>({});
const loading = reactive({
  submit: false,
});
const save = async (type: string) => {
  await FormRef.value.validate();
  loading.submit = true;
  if (type === 'submit') {
    branchForm.value.isSubmit = true;
    branchForm.value.processDefinitionKey = 'fct_project_meeting_party';
  }
  let api = addBranchApi;
  if (branchForm.value.id) {
    api = editBranchApi;
  }
  try {
    const res = await api(branchForm.value as BranchInfo);
    message.success('保存成功');
    closePopup();
    emit('ok', res);
  } finally {
    loading.submit = false;
  }
};
const selectReview = (_value: number, data: ReviewInfo) => {
  branchForm.value.reviewNodeName = `关于【${data.projectName}】的请示`;
  branchForm.value.projectName = data.projectName;
  branchForm.value.projectId = data.projectId;
  branchForm.value.backgroundDesc = data.backgroundDesc;
};
const rules = {
  reviewId: [{ required: true, message: '请选择关联项目评审会议', trigger: 'change' }],
  reviewNodeName: [{ required: true, message: '请输入党支部会议名称', trigger: 'blur' }],
};
const formProp = { ...FORM_PROP, labelCol: { span: 6 }, wrapperCol: { span: 18 } };
const colSpan = COL_SPAN_PROP;
const fullProp = { ...FULL_FORM_ITEM_PROP, labelCol: { span: 3 }, wrapperCol: { span: 21 } };
</script>

<template>
  <BasicPopup v-bind="$attrs" title="党支部会议申请" @register="registerPopup">
    <template #insertToolbar>
      <a-space>
        <a-button type="primary" :loading="loading.submit" @click="save('save')">保存</a-button>
        <a-button type="primary" :loading="loading.submit" @click="save('submit')">提交</a-button>
      </a-space>
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-form ref="FormRef" class="mt-5" :model="branchForm" :rules="rules" v-bind="formProp">
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="关联项目评审会议" name="reviewId">
              <ApiComponent
                v-model="branchForm.reviewId as unknown as string"
                :component="Select"
                :api="getReviewMeetingListApi"
                :params="{ status: 'EFFECTIVE' }"
                label-field="reviewNodeName"
                value-field="reviewId"
                model-prop-name="value"
                @change="selectReview"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="项目名称" name="projectName">
              <a-input v-model:value="branchForm.projectName" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="党支部会议名称" name="reviewNodeName">
              <a-input v-model:value="branchForm.reviewNodeName" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="项目背景" name="backgroundDesc" v-bind="fullProp">
              <a-textarea v-model:value="branchForm.backgroundDesc" :rows="4" class="w-full" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="项目风险点及建议" name="riskMitigationDesc" v-bind="fullProp">
              <a-textarea v-model:value="branchForm.riskMitigationDesc" :rows="4" class="w-full" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="项目结论" name="resultDesc" v-bind="fullProp">
              <a-textarea v-model:value="branchForm.resultDesc" :rows="4" class="w-full" />
            </a-form-item>
          </a-col>
        </a-row>
        <BaseAttachmentList
          v-model="branchForm.attachmentList"
          :business-id="branchForm.id"
          business-type="FCT_PROJECT_MEETING_PARTY"
          edit-mode
        />
      </a-form>
    </div>
  </BasicPopup>
</template>

<style></style>
