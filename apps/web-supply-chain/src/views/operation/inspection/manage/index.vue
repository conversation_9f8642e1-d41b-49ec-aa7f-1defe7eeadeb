<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';
import { defineFormOptions, downloadAndSaveWithAutoFileName } from '@vben/utils';

import { message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { deleteInspectionApi, getDownloadFileLinkApi, getInspectionPageApi } from '#/api';

import InspectionDetail from './inspection-detail.vue';
import InspectionEdit from './inspection-edit.vue';

const dictStore = useDictStore();
const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'reportName',
      label: '报告名称',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '项目名称',
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '状态',
      componentProps: {
        options: dictStore.getDictList('BUS_STATUS'),
      },
    },
    {
      component: 'Input',
      fieldName: 'reportCode',
      label: '报告编号',
    },
  ],
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'reportCode', title: '报告编号' },
    { field: 'reportName', title: '报告名称' },
    { field: 'projectName', title: '项目名称' },
    {
      field: 'status',
      title: '业务状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'BUS_STATUS',
        },
      },
    },
    {
      field: 'approvalStatus',
      title: '审批状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'baseEnableType',
        },
      },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 220,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async (
        { page }: { page: { currentPage: number; pageSize: number } },
        formValues: { categoryCode: string; categoryName: string; status: string },
      ) => {
        return await getInspectionPageApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  checkboxConfig: {
    showHeader: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents: {
    checkboxChange: ({ checked, row }: { checked: boolean; row: unknown }) => {
      if (checked) {
        gridApi.grid.setAllCheckboxRow(false);
        gridApi.grid.setCheckboxRow(row, true);
      }
    },
  },
});
const [registerEditPage, { openPopup: openEditPopup }] = usePopup();
const [registerDetailPage, { openPopup: openDetailPopup }] = usePopup();
const add = () => {
  openEditPopup(true, {});
};
const edit = (row: { id: number }) => {
  openEditPopup(true, row);
};
const viewDetail = (row: { id: number }) => {
  openDetailPopup(true, row);
};
const download = async (row: { fileId: number }) => {
  const url = await getDownloadFileLinkApi({ id: row.fileId });
  await downloadAndSaveWithAutoFileName({ url });
};
const del = async () => {
  const rows = gridApi.grid.getCheckboxRecords();
  if (rows.length === 0) {
    return message.warning('请先选择一条数据');
  }
  const id = rows[0].id;
  await deleteInspectionApi(id);
  await gridApi.reload();
  message.success('删除成功');
};
const editSuccess = () => {
  gridApi.reload();
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-space>
          <a-button type="primary" @click="add">新增</a-button>
          <a-button type="primary" danger @click="del">删除</a-button>
        </a-space>
      </template>
      <template #action="{ row }">
        <a-space>
          <a-typography-link @click="viewDetail(row)">详情</a-typography-link>
          <a-typography-link @click="edit(row)">编辑</a-typography-link>
          <a-typography-link @click="download(row)">下载</a-typography-link>
        </a-space>
      </template>
    </Grid>
    <InspectionEdit @register="registerEditPage" @ok="editSuccess" />
    <InspectionDetail @register="registerDetailPage" />
  </Page>
</template>

<style></style>
