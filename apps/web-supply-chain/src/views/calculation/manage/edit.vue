<script setup lang="ts">
import type { TableColumnType } from 'ant-design-vue';
import type { SelectValue } from 'ant-design-vue/es/select';

import type {
  CategoryIndicatorBO,
  CompanyIndicatorInfo,
  IndicatorListInfo,
  ScoringAdjustRuleBO,
  ScoringCompanyVO,
  ScoringIndicatorRuleBO,
} from '#/api';

import { computed, reactive, ref, watch } from 'vue';

import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { formatDate } from '@vben/utils';

import { Alert, Input, InputNumber, message, Select, Table } from 'ant-design-vue';
import BigNumber from 'bignumber.js';
import { find, merge } from 'lodash-es';

import {
  getCompanyIndicatorDetailApi,
  getCompanyIndicatorHistoryApi,
  getIndicatorConfigListApi,
  saveCompanyIndicatorApi,
  submitCompanyIndicatorApi,
} from '#/api';
import { useWorkflowBase } from '#/composables/useWorkflowBase';

const emit = defineEmits(['register', 'ok']);
const state = reactive({
  id: 0,
  pageType: 'edit',
  loading: {
    submit: false,
  },
});
const {
  startWorkflow,
  WorkflowPreviewModal,
  useOperationButton,
  useViewButton,
  initWorkflow,
  isProcessInstance,
  isWorkflow,
  isRunningTask,
  isWorkflowLoading,
} = useWorkflowBase();
const OperationButton = useOperationButton();
const ViewButton = useViewButton();
const detailInfo = ref<ScoringCompanyVO>({});
const pageTitle = computed(() => {
  if (state.pageType === 'detail') {
    return '额度测算详情';
  } else {
    return state.id ? '编辑额度测算' : '新增额度测算';
  }
});
const init = async (data: any) => {
  state.id = data.id;
  state.pageType = data.pageType;
  await initWorkflow({ formKey: 'scm_company_score', businessKey: data.id });
  changeLoading(true);
  if (!data.id) {
    detailInfo.value.companyCode = data.companyCode;
    detailInfo.value.companyName = data.companyName;
    await getIndicatorConfig();
    try {
      await getCompanyHistoryIndicatorList();
    } catch (error) {
      console.error(error);
    }
  }
  if (state.id) {
    try {
      await getCompanyIndicatorDetail();
      await getIndicatorConfig(detailInfo.value.scoringConfigId);
      handleDetail();
      await getCompanyHistoryIndicatorList();
    } catch (error) {
      console.error(error);
    }
  }
  changeLoading(false);
};
const [registerPopup, { changeLoading, changeOkLoading, closePopup }] = usePopupInner(init);
const config = ref<IndicatorListInfo>({});
const tableData = ref<CategoryIndicatorBO[]>([]);

// 计算单元格合并的逻辑
const calculateSpan = (dataIndex: string, currentIndex: number) => {
  const data = tableData.value ?? [];
  const currentRow = data?.[currentIndex];

  // 如果当前行没有对应的字段值，不合并
  if (!currentRow || currentRow[dataIndex] === undefined || currentRow[dataIndex] === null) {
    return { rowspan: 1, colspan: 1 };
  }

  const currentValue = currentRow[dataIndex];

  // 检查是否是该值的第一次出现
  let isFirstOccurrence = true;
  for (let i = 0; i < currentIndex; i++) {
    const row = data?.[i];
    if (row && row[dataIndex] === currentValue) {
      isFirstOccurrence = false;
      break;
    }
  }

  // 如果不是第一次出现，返回 rowspan: 0 来隐藏单元格
  if (!isFirstOccurrence) {
    return { rowspan: 0, colspan: 1 };
  }

  // 计算连续相同值的行数
  let rowspan = 1;
  for (let i = currentIndex + 1; i < data.length; i++) {
    const row = data?.[i];
    if (row && row[dataIndex] === currentValue) {
      rowspan++;
    } else {
      break;
    }
  }

  return { rowspan, colspan: 1 };
};

const handleSpan = (_: CategoryIndicatorBO, index: number | undefined, column: any) => {
  const dataIndex = column.dataIndex;

  // 只对 index 和 categoryName 列进行合并
  if (dataIndex === 'index' || dataIndex === 'categoryName') {
    return calculateSpan(dataIndex, index as number);
  }

  return { rowspan: 1, colspan: 1 };
};

const columns: TableColumnType[] = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    width: 80,
    customCell: (record: any, index: number | undefined) => handleSpan(record, index, { dataIndex: 'index' }),
  },
  {
    title: '指标分类',
    dataIndex: 'categoryName',
    key: 'categoryName',
    width: 200,
    customCell: (record: any, index: number | undefined) => handleSpan(record, index, { dataIndex: 'categoryName' }),
  },
  { title: '指标名称', dataIndex: 'indicatorName', key: 'indicatorName' },
  { title: '数值', dataIndex: 'value', key: 'value', width: 300 },
  { title: '得分', dataIndex: 'score', key: 'score', width: 200 },
  { title: '权重', dataIndex: 'weight', key: 'weight', width: 100 },
  { title: '加权得分', dataIndex: 'weightedScore', key: 'weightedScore', width: 100 },
  { title: '打分标准', dataIndex: 'standard', key: 'standard', width: 400 },
];
const historyColumns: TableColumnType[] = [
  { title: '测算日期', dataIndex: 'scoringDate', key: 'scoringDate' },
  { title: '测算总分', dataIndex: 'finalScore', key: 'finalScore' },
  { title: '测算额度上限', dataIndex: 'scoringLimit', key: 'scoringLimit' },
  { title: '状态', dataIndex: 'status', key: 'status' },
  { title: '审批状态', dataIndex: 'approvalStatus', key: 'approvalStatus' },
];
const getIndicatorConfig = async (id?: number) => {
  config.value = await getIndicatorConfigListApi({ id });
  const data = config.value.categoryIndicators ?? [];
  const adjustRules: (ScoringAdjustRuleBO & ScoringIndicatorRuleBO)[] = [];
  if (config.value.adjustRules) {
    config.value.adjustRules.forEach((o) => {
      adjustRules.push({
        ruleScore: o.ruleFactor,
        ...o,
      });
    });
  }
  data.push(
    {
      extraType: 'total',
      indicatorName: '总分',
    },
    {
      extraType: 'adjust',
      categoryName: '调整因子',
      indicatorName: '客户分级',
      indicatorRules: adjustRules,
      inputType: 'SELECT',
    },
    {
      extraType: 'adjustTotal',
      indicatorName: '调整后总分',
    },
  );
  tableData.value = data;
};
const getCompanyIndicatorDetail = async () => {
  detailInfo.value = await getCompanyIndicatorDetailApi({ id: state.id });
};
const handleDetail = () => {
  tableData.value.forEach((o: CategoryIndicatorBO) => {
    const detail = find(detailInfo.value.scoringDetails, { indicatorId: o.indicatorId });
    if (detail) {
      o.value = detail.value;
      o.score = detail.score;
      o.weightedScore = new BigNumber(detail.score ?? 0).times(o.weight ?? 0).toNumber();
    }
  });
  const adjust = tableData.value.find((o: CategoryIndicatorBO) => o.extraType === 'adjust')!;
  const detailAdjust = detailInfo.value.scoringDetails?.find((o: any) => o.extraType === 'adjust');
  if (detailAdjust) {
    adjust.value = detailAdjust.value;
    adjust.score = detailAdjust.score;
    adjust.weightedScore = detailAdjust.score;
  }
};
const historyList = ref<CompanyIndicatorInfo[]>([]);
const getCompanyHistoryIndicatorList = async () => {
  if (!detailInfo.value.companyCode) {
    return false;
  }
  historyList.value = await getCompanyIndicatorHistoryApi({ companyCode: detailInfo.value.companyCode });
};
const changeSelectValue = (record: CategoryIndicatorBO, value?: SelectValue, option?: ScoringIndicatorRuleBO) => {
  if (record.extraType === 'adjust') {
    record.score = option?.ruleFactor;
    record.weightedScore = option?.ruleFactor;
  } else {
    record.score = value;
    record.weightedScore = new BigNumber(record.score).times(record.weight ?? 0).toNumber();
  }
};
const changeRangeValue = (record: CategoryIndicatorBO, value?: number) => {
  // 如果没有值或者没有规则，重置得分
  if (value === undefined || value === null || !record.indicatorRules || record.indicatorRules.length === 0) {
    record.score = 0;
    record.weightedScore = 0;
    return;
  }

  // 根据区间规则计算得分
  let matchedScore = 0;

  for (const rule of record.indicatorRules) {
    const minValue = rule.minValue ? Number.parseFloat(rule.minValue) : null;
    const maxValue = rule.maxValue ? Number.parseFloat(rule.maxValue) : null;

    // 判断值是否落在当前区间内
    // minValue 是包含的下限，maxValue 是不包含的上限
    let isInRange = false;

    if (minValue !== null && maxValue !== null) {
      // 有上下界的区间：[minValue, maxValue)
      isInRange = value >= minValue && value < maxValue;
    } else if (minValue !== null && maxValue === null) {
      // 只有下界：[minValue, +∞)
      isInRange = value >= minValue;
    } else if (minValue === null && maxValue !== null) {
      // 只有上界：(-∞, maxValue)
      isInRange = value < maxValue;
    }

    if (isInRange) {
      matchedScore = rule.ruleScore ?? 0;
      break;
    }
  }

  // 设置得分和加权得分
  record.score = matchedScore;
  record.weightedScore = new BigNumber(record.score).times(record.weight || 0).toNumber();
};
const changeScoreValue = (record: CategoryIndicatorBO, value?: number) => {
  record.weightedScore = new BigNumber(value ?? 0).times(record.weight ?? 0).toNumber();
};
watch(
  () => tableData.value,
  () => {
    const sourceList = tableData.value.filter(
      (o: CategoryIndicatorBO) => !['adjust', 'adjustTotal', 'total'].includes(o.extraType ?? ''),
    );
    let total = new BigNumber(0);
    sourceList.forEach((o: CategoryIndicatorBO) => {
      total = total.plus(o.weightedScore ?? 0);
    });
    tableData.value.find((o: CategoryIndicatorBO) => o.extraType === 'total')!.weightedScore = total.toNumber();
    const adjust = tableData.value.find((o: CategoryIndicatorBO) => o.extraType === 'adjust')!.score;
    tableData.value.find((o: CategoryIndicatorBO) => o.extraType === 'adjustTotal')!.weightedScore = total
      .times(adjust ?? 1)
      .toNumber();
  },
  { deep: true },
);
const save = async (type: string) => {
  const sourceList = tableData.value.filter(
    (o: CategoryIndicatorBO) => !['adjust', 'adjustTotal', 'total'].includes(o.extraType ?? ''),
  );
  for (const o of sourceList) {
    if (!o.value) {
      return message.error(`请填写${o.indicatorName}的数值`);
    }
    if (!o.score) {
      return message.error(`请填写${o.indicatorName}的得分`);
    }
  }
  const adjust = tableData.value.find((o: CategoryIndicatorBO) => o.extraType === 'adjust')!.value;
  if (!adjust) {
    return message.error('请选择客户分级');
  }
  const scoringDetails: any[] = [];
  tableData.value.forEach((o: CategoryIndicatorBO) => {
    scoringDetails.push({
      extraType: o.extraType,
      categoryId: o.categoryId,
      indicatorId: o.indicatorId,
      value: o.value,
      score: o.score,
    });
  });
  const formData = merge(detailInfo.value, {
    scoringConfigId: config.value.id,
    initialScore: tableData.value.find((o: CategoryIndicatorBO) => o.extraType === 'total')!.weightedScore,
    finalScore: tableData.value.find((o: CategoryIndicatorBO) => o.extraType === 'adjustTotal')!.weightedScore,
    scoringDetails,
  });
  changeOkLoading(true);
  state.loading.submit = true;
  const api = type === 'submit' ? submitCompanyIndicatorApi : saveCompanyIndicatorApi;
  try {
    if (type === 'submit') {
      const { processDefinitionKey, startUserSelectAssignees } = await startWorkflow();
      formData.processDefinitionKey = processDefinitionKey;
      formData.startUserSelectAssignees = startUserSelectAssignees;
    }
    await api(formData);
    message.success('保存成功');
    emit('ok');
    closePopup();
  } finally {
    state.loading.submit = false;
    changeOkLoading(false);
  }
};
const handleWeight = (record: CategoryIndicatorBO) => {
  return new BigNumber(record.weight ?? 0).times(100);
};
const setRangeMin = (record: CategoryIndicatorBO) => {
  if (record.signType === 1) {
    return 0;
  } else if (record.signType === -1) {
    return new BigNumber(record.minValue).negated().toNumber();
  }
};
const setRangeMax = (record: CategoryIndicatorBO) => {
  if (record.signType === 1) {
    return Number(record.maxScore);
  } else if (record.signType === -1) {
    return 0;
  }
};
const workflowSuccess = () => {
  emit('ok');
  closePopup();
};
</script>

<template>
  <BasicPopup
    v-bind="$attrs"
    :show-ok-btn="state.pageType === 'edit'"
    :title="pageTitle"
    @register="registerPopup"
    @ok="save"
  >
    <template #insertToolbar>
      <a-space v-if="state.pageType === 'edit'">
        <a-button type="primary" :loading="state.loading.submit" @click="save('submit')">提交</a-button>
      </a-space>
      <div v-if="state.pageType === 'audit'" v-loading="isWorkflowLoading">
        <OperationButton v-if="isProcessInstance" @success="workflowSuccess" />
      </div>
      <ViewButton v-if="isWorkflow && !isRunningTask && state.pageType === 'detail'" />
    </template>
    <div class="px-4">
      <a-descriptions class="pt-4">
        <a-descriptions-item label="企业名称">
          {{ detailInfo.companyName }}
        </a-descriptions-item>
        <a-descriptions-item label="统一社会信用代码">
          {{ detailInfo.companyCode }}
        </a-descriptions-item>
      </a-descriptions>
      <BasicCaption content="测算信息" />
      <Table :data-source="tableData" :columns="columns" :pagination="false">
        <template #bodyCell="{ column, record }">
          <template v-if="state.pageType !== 'detail'">
            <template v-if="column.key === 'value'">
              <template v-if="!['adjustTotal', 'total'].includes(record.extraType ?? '')">
                <Select
                  v-if="record.inputType === 'SELECT'"
                  v-model:value="record.value"
                  :options="record.indicatorRules"
                  :field-names="{ label: 'ruleName', value: 'ruleScore' }"
                  class="w-full max-w-[300px]"
                  @change="(value: SelectValue, option) => changeSelectValue(record, value, option)"
                />
                <InputNumber
                  v-else-if="record.inputType === 'RANGE'"
                  v-model:value="record.value"
                  :controls="false"
                  class="w-full max-w-[300px]"
                  @change="(value: SelectValue) => changeRangeValue(record, value as number)"
                />
                <Input
                  v-else-if="record.inputType === 'INPUT'"
                  v-model:value="record.value"
                  class="w-full max-w-[300px]"
                />
              </template>
            </template>
            <template v-if="column.key === 'score'">
              <InputNumber
                v-if="record.inputType === 'INPUT'"
                v-model:value="record.score"
                :controls="false"
                :min="setRangeMin(record)"
                :max="setRangeMax(record)"
                class="w-full max-w-[200px]"
                @change="(value) => changeScoreValue(record, value as number)"
              />
            </template>
            <template v-if="column.key === 'weight'">
              <span v-if="!record.extraType">{{ handleWeight(record) }}%</span>
            </template>
          </template>
          <template v-if="column.key === 'standard'">
            <p v-for="(item, index) in record.indicatorRules" :key="index">
              <span v-if="record.extraType === 'adjust'">{{ item.ruleName }}：总得分 * {{ item.ruleScore }}</span>
              <span v-else>{{ item.ruleName }}：{{ Number(item.ruleScore) }}</span>
            </p>
          </template>
        </template>
      </Table>
      <Alert type="info" class="my-4">
        <template #message>
          <div class="whitespace-pre-wrap">
            {{ config.description }}
          </div>
        </template>
      </Alert>
      <BasicCaption content="测算历史" />
      <Table :data-source="historyList" :columns="historyColumns" :pagination="false" class="pb-4">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'scoringDate'">
            {{ formatDate(record.scoringDate) }}
          </template>
        </template>
      </Table>
    </div>
    <WorkflowPreviewModal v-if="state.pageType === 'edit'" />
  </BasicPopup>
</template>

<style></style>
