import { defineConfig } from '@vben/vite-config';

export default defineConfig(async () => {
  return {
    application: {},
    vite: {
      server: {
        port: 80,
        allowedHosts: ['gylxt.jxctjtgyl.com.cn'],
        proxy: {
          '/jxct/api': {
            changeOrigin: true,
            // mock代理目标地址
            target: 'http://*************',
            ws: true,
          },
          // '/jxct/api': {
          //   changeOrigin: true,
          //   rewrite: (path) => path.replace(/^\/jxct\/api/, ''),
          //   // mock代理目标地址
          //   target: 'http://localhost:5320/api',
          //   ws: true,
          // },
        },
      },
    },
  };
});
