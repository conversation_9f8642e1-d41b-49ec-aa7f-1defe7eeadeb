<script lang="ts">
import type { CSSProperties } from 'vue';

import type { Nullable, Recordable } from '@vben/types';

import type { PopupInstance, PopupProps } from './typing';

import { computed, defineComponent, getCurrentInstance, nextTick, ref, unref, watch } from 'vue';

import { $t as t } from '@vben/locales';

import { ScrollContainer } from '#/components/Container';
import { useAttrs } from '#/hooks/core/useAttrs';
import { useDesign } from '#/hooks/web/useDesign';
import { deepMerge } from '#/utils';
import { isFunction, isNumber } from '#/utils/is';

import PopupHeader from './components/PopupHeader.vue';
import { basicProps } from './props';

export default defineComponent({
  components: { ScrollContainer, PopupHeader },
  inheritAttrs: false,
  props: basicProps,
  emits: ['openChange', 'ok', 'close', 'register'],
  setup(props, { emit }) {
    const openRef = ref(false);
    const fullScreenRef = ref(false);
    const attrs: any = useAttrs({ excludeDefaultKeys: false });
    const propsRef = ref<Partial<Nullable<PopupProps>>>(null);

    const { prefixCls } = useDesign('basic-popup');

    const popupInstance: PopupInstance = {
      setPopupProps,
      emitOpen: undefined,
    };

    const instance = getCurrentInstance();

    instance && emit('register', popupInstance, instance.uid);

    const getMergeProps = computed((): PopupProps => {
      return deepMerge(props, unref(propsRef)) as any;
    });

    const getProps = computed((): PopupProps => {
      const opt = {
        ...unref(attrs),
        ...unref(getMergeProps),
        open: unref(openRef),
      };
      return opt as unknown as PopupProps;
    });

    const getBindValues = computed((): PopupProps => {
      return {
        ...attrs,
        ...unref(getProps),
      };
    });
    const getWrapperClass = computed(() => {
      return [
        prefixCls,
        unref(attrs).class,
        'p-4',
        {
          'fullscreen-popup': !!unref(fullScreenRef),
        },
      ];
    });

    const getBodyStyle = computed((): CSSProperties => {
      const { width, zIndex, height } = unref(getProps);
      return {
        zIndex,
        width: width ? (isNumber(width) ? `${width}px` : width) : '100%',
        margin: '0 auto',
        height,
      };
    });
    const getScrollContentStyle = computed((): CSSProperties => {
      return {
        position: 'relative',
        height: `100%`,
      };
    });

    const getLoading = computed(() => {
      return !!unref(getProps)?.loading;
    });

    // 控制是否渲染内容，考虑 destroyOnClose 属性
    const shouldRenderContent = computed(() => {
      // 直接从 props 和 propsRef 获取 destroyOnClose，避免依赖复杂的计算属性链
      const mergedProps = deepMerge(props, unref(propsRef) || {});
      const destroyOnClose = mergedProps.destroyOnClose;
      const isOpen = unref(openRef);

      if (destroyOnClose) {
        // 如果 destroyOnClose 为 true，只有在弹窗打开时才渲染内容
        return isOpen;
      }
      // 如果 destroyOnClose 为 false 或未设置，始终渲染内容（只是控制显示/隐藏）
      return true;
    });

    watch(
      () => props.open,
      (newVal, oldVal) => {
        if (newVal !== oldVal) openRef.value = newVal;
      },
      { deep: true },
    );

    watch(openRef, (open) => {
      nextTick(() => {
        if (open) fullScreenRef.value = !!props.defaultFullscreen;
        emit('openChange', open);
        instance && popupInstance.emitOpen?.(open, instance.uid);
      });
    });

    // Cancel event
    async function onClose(e: Recordable<any>) {
      const { closeFunc } = unref(getProps);
      emit('close', e);
      if (closeFunc && isFunction(closeFunc)) {
        const res = await closeFunc();
        openRef.value = !res;
        return;
      }
      openRef.value = false;
    }

    function setPopupProps(props: Partial<PopupProps>): void {
      // Keep the last setPopupProps
      propsRef.value = deepMerge(unref(propsRef) || ({} as any), props);
      if (Reflect.has(props, 'open')) {
        openRef.value = !!props.open;
      }
      if (Reflect.has(props, 'defaultFullscreen')) {
        fullScreenRef.value = !!props.defaultFullscreen;
      }
    }

    function handleOk() {
      emit('ok');
    }

    return {
      openRef,
      onClose,
      t,
      prefixCls,
      getMergeProps: getMergeProps as any,
      getBodyStyle,
      getScrollContentStyle,
      getProps: getProps as any,
      getWrapperClass,
      getLoading,
      getBindValues,
      handleOk,
      shouldRenderContent,
    };
  },
});
</script>
<template>
  <div :class="getWrapperClass" v-if="openRef">
    <div :class="`${prefixCls}-main bg-card border`">
      <PopupHeader v-bind="getProps" @close="onClose" @ok="handleOk">
        <template #[item]="data" v-for="item in Object.keys($slots)">
          <slot :name="item" v-bind="data || {}"></slot>
        </template>
      </PopupHeader>
      <div :class="`${prefixCls}-body`">
        <ScrollContainer
          :style="getScrollContentStyle"
          v-loading="getLoading"
          :loading-tip="loadingText || t('common.loadingText')"
        >
          <div :style="getBodyStyle">
            <slot v-if="shouldRenderContent"></slot>
          </div>
        </ScrollContainer>
      </div>
    </div>
  </div>
</template>
<style lang="less">
@import '#/style/index.less';
@header-height: 60px;
@prefix-cls: ~'@{namespace}-basic-popup';
@prefix-cls-body: ~'@{namespace}-basic-popup-body';
@prefix-cls-main: ~'@{namespace}-basic-popup-main';

.@{prefix-cls} {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  //background-color: @component-background;
  z-index: 500;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .@{prefix-cls-main} {
    width: 100%;
    height: 100%;
    border-radius: var(--radius);
    overflow: hidden;
  }
  &.full-popup {
    .@{prefix-cls-body} {
      & > .scrollbar {
        & > .scrollbar__bar {
          display: none !important;
        }
        & > .scrollbar__wrap {
          & > .scrollbar__view {
            height: 100%;
            overflow: hidden;
            padding: 0;
            & > div {
              height: 100%;
            }
          }
        }
      }
    }
  }
  &.fullscreen-popup {
    position: fixed;
    left: 0;
    top: 0;
    z-index: 1000;
  }
  .word-form {
    margin-bottom: 0;
  }

  .@{prefix-cls-body} {
    flex: 1;
    height: calc(100% - @header-height);
    padding: 0;
    background-color: @component-background;
    overflow: hidden;

    & > .scrollbar > .scrollbar__wrap {
      margin-bottom: 0 !important;
      & > .scrollbar__view {
        padding: 10px 0;
      }
    }

    > .scrollbar > .scrollbar__bar.is-horizontal {
      display: none;
    }
  }
}
</style>
