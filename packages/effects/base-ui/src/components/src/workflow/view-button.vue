<script setup lang="ts">
import type { BpmProcessInstanceApi } from '#/components/src/workflow/type';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { formatPast2 } from '@vben/utils';

import { Button, Table, TabPane, Tabs } from 'ant-design-vue';

import { StatusTag, WorkflowSimpleViewer, WorkflowTimeline } from '#/components';

const props = defineProps({
  processInstanceId: {
    type: String,
    required: true,
  },
  activityNodes: {
    type: Array as () => BpmProcessInstanceApi.ApprovalNodeInfo[],
    required: true,
  },
  taskApi: {
    type: Object,
    required: true,
  },
});
const columns = [
  {
    title: '审批节点',
    dataIndex: 'name',
    maxWidth: 120,
  },
  {
    title: '审批人',
    dataIndex: 'approver',
    key: 'approver',
    maxWidth: 120,
  },
  {
    title: '开始时间',
    dataIndex: 'createTime',
    maxWidth: 180,
  },
  {
    title: '结束时间',
    dataIndex: 'endTime',
    maxWidth: 180,
  },
  {
    title: '审批状态',
    dataIndex: 'status',
    key: 'status',
    maxWidth: 150,
  },
  {
    title: '审批建议',
    dataIndex: 'reason',
    maxWidth: 200,
    ellipsis: true,
  },
  {
    title: '耗时',
    dataIndex: 'durationInMillis',
    maxWidth: 180,
    customRender: ({ text }: { text: number }) => {
      return formatPast2(text);
    },
  },
];
const modelView = ref<any>({});
const activeTab = ref('record');
const [Modal, modalApi] = useVbenModal({
  showConfirmButton: false,
  cancelText: '关闭',
});

async function viewDetail() {
  modelView.value = await props.taskApi.getProcessInstanceBpmnModelView(props.processInstanceId);
  modalApi.open();
}
</script>

<template>
  <div>
    <Button @click="viewDetail">查看流程详情</Button>
    <Modal title="流程详情" class="min-h-[300px] w-[1000px]">
      <Tabs v-model:active-key="activeTab">
        <TabPane key="record" tab="流转记录">
          <Table :columns="columns" :data-source="modelView.tasks">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'approver'">
                {{ record.assigneeUser?.nickname || record.ownerUser?.nickname || '-' }}
              </template>
              <template v-if="column.key === 'status'">
                <StatusTag
                  :value="record.status"
                  code="BPM_PROCESS_INSTANCE_STATUS"
                  :option="{ dictValueType: 'number' }"
                />
              </template>
            </template>
          </Table>
        </TabPane>
        <TabPane key="timeline" tab="流程节点">
          <WorkflowTimeline :activity-nodes="activityNodes" />
        </TabPane>
        <TabPane key="flow" tab="流程图">
          <div class="h-full w-full">
            <WorkflowSimpleViewer :model-view="modelView" />
          </div>
        </TabPane>
      </Tabs>
    </Modal>
  </div>
</template>

<style></style>
