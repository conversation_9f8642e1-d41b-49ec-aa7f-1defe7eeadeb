import type { BpmModelApi, BpmProcessInstanceApi } from '#/components/src/workflow/type';

import { computed, defineComponent, h, nextTick, ref } from 'vue';

import { confirm, prompt, useVbenModal } from '@vben/common-ui';

import { message, Select } from 'ant-design-vue';

import { BpmCandidateStrategyEnum, BpmTaskStatusEnum } from '#/components/src/workflow/constants';
import OperationButton from '#/components/src/workflow/operation-button.vue';
import PreviewModal from '#/components/src/workflow/preview-modal.vue';
import ViewButton from '#/components/src/workflow/view-button.vue';

export { OperationButton };
export { ViewButton };

export interface WorkflowConfig {
  key?: string;
  processInstanceId?: string;
  processDefinitionId?: string;
  formKey?: string;
  businessKey?: number;
}

export interface WorkflowApiConfig {
  getProcessDefinitionApi: (params: { id?: string; key?: string }) => Promise<any>;
  getApprovalDetailApi: (params: {
    [key: string]: any;
    processDefinitionId?: string;
    processInstanceId?: string;
  }) => Promise<any>;
  getProcessDefinitionListByFormKeyApi: (params: { formKey: string }) => Promise<any>;
  getUserInfoByIdsApi: (ids: number[]) => Promise<any>;
  getUserListByKeywordApi: (params: { keyword: string }) => Promise<any>;
  getUserTreeListApi: (params: { name: string; orgId: number }) => Promise<any>;
  [key: string]: (...args: any[]) => Promise<any>;
}

export function useWorkflow(apiConfig: WorkflowApiConfig) {
  const config = ref<WorkflowConfig>({});
  const processDefinition = ref<any>({});
  const approvalDetail = ref<any>({});
  const processInstance = ref<any>({});
  const processDefinitionList = ref<BpmModelApi.ProcessDefinition[]>([]);
  const OperationButtonRef = ref<any>(null);
  const isWorkflow = computed(() => !!config.value.key);
  const isProcessInstance = computed(() => !!config.value.processInstanceId);
  const isWorkflowLoading = ref(false);
  const isRunningTask = computed(() => approvalDetail.value.status === BpmTaskStatusEnum.RUNNING);

  let currentPromiseResolve: ((value: any) => void) | null = null;
  let currentPromiseReject: ((reason?: any) => void) | null = null;
  const [WorkflowPreviewModal, previewModalApi] = useVbenModal({
    connectedComponent: PreviewModal,
    destroyOnClose: true,
    onConfirm: async () => {
      const data = previewModalApi.getData();
      // 获取发起人自选的任务
      const startUserSelectTasks =
        approvalDetail.value.activityNodes?.filter(
          (node: BpmProcessInstanceApi.ApprovalNodeInfo) =>
            BpmCandidateStrategyEnum.START_USER_SELECT === node.candidateStrategy,
        ) || [];
      const startUserSelectAssignees: Record<string, string[]> = {};
      if (startUserSelectTasks?.length > 0) {
        if (!data.activityId || !Array.isArray(data.userList)) {
          message.warning('请选择候选人');
          return false;
        }
        startUserSelectAssignees[data.activityId] = data.userList.map((item) => item.id);
        for (const userTask of startUserSelectTasks) {
          const assignees = startUserSelectAssignees[userTask.id];
          if (Array.isArray(assignees) && assignees.length === 0) {
            message.warning(`请选择${userTask.name}的候选人`);
            return false;
          }
        }
      }
      if (currentPromiseResolve) {
        currentPromiseResolve(startUserSelectAssignees);
      }
      await previewModalApi.close();
      currentPromiseResolve = null;
      currentPromiseReject = null;
      await previewModalApi.close();
    },
    onClosed: () => {
      if (currentPromiseReject) {
        currentPromiseReject(new Error('取消选择'));
      }
      currentPromiseResolve = null;
      currentPromiseReject = null;
    },
  });
  const getProcessDefinitionDetail = async () => {
    if (!config.value.key) return;
    try {
      processDefinition.value = await apiConfig.getProcessDefinitionApi({ key: config.value.key });
    } catch (error) {
      console.error('获取流程定义失败', error);
    }
  };
  const getApprovalDetail = async () => {
    if (!config.value.processInstanceId && !config.value.processDefinitionId) return;
    approvalDetail.value = await apiConfig.getApprovalDetailApi({
      processInstanceId: config.value.processInstanceId,
      processDefinitionId: config.value.processDefinitionId,
    });
    processInstance.value = approvalDetail.value.processInstance;
  };
  const getProcessDefinitionList = async () => {
    if (!config.value.formKey) return;
    processDefinitionList.value = await apiConfig.getProcessDefinitionListByFormKeyApi({
      formKey: config.value.formKey,
    });
  };
  const initWorkflow = async (data?: WorkflowConfig) => {
    config.value = Object.assign(config.value, data);
    if (!apiConfig.getWorkflowConfigApi) return;
    isWorkflowLoading.value = true;
    try {
      const res = await apiConfig.getWorkflowConfigApi({
        formKey: config.value.formKey,
        businessKey: config.value.businessKey,
      });
      if (res) {
        config.value.key = res.processDefinitionKey;
        config.value.processInstanceId = res.processInstanceId;
        config.value.processDefinitionId = res.processDefinitionId;
        await getProcessDefinitionDetail();
        await getApprovalDetail();
        await nextTick();
        OperationButtonRef.value?.loadTodoTask(approvalDetail.value.todoTask);
      } else {
        await getProcessDefinitionList();
      }
    } catch (error) {
      console.error('获取流程定义失败', error);
    } finally {
      isWorkflowLoading.value = false;
    }
  };
  const startWorkflow = async () => {
    let key: string | undefined;
    let processDefinition: BpmModelApi.ProcessDefinition | undefined;
    if (processDefinitionList.value.length === 0) {
      await confirm('未匹配到可用的审批流程，提交将立即生效，是否确认？', '提示');
    } else if (processDefinitionList.value.length === 1 && processDefinitionList.value[0]) {
      key = processDefinitionList.value[0].key;
      config.value.key = key;
      processDefinition = processDefinitionList.value[0];
      config.value.processDefinitionId = processDefinition?.id;
    } else {
      key = await prompt({
        component: Select,
        componentProps: {
          options: processDefinitionList.value,
          placeholder: '请选择',
          popupClassName: 'pointer-events-auto',
          fieldNames: { label: 'name', value: 'key' },
        },
        content: '',
        title: '选择审批流程',
        modelPropName: 'value',
      });
      config.value.key = key;
      processDefinition = processDefinitionList.value.find((item: any) => item.key === key);
      config.value.processDefinitionId = processDefinition?.id;
    }
    await getApprovalDetail();
    const startUserSelectAssignees = await openTimelineModal();
    return { processDefinitionKey: key, startUserSelectAssignees };
  };

  const openTimelineModal = async (api?: any) => {
    if (currentPromiseResolve) {
      console.error('Another timeline modal is already open and waiting for resolution.');
      throw new Error('Modal conflict: A modal is already open.');
    }
    if (!approvalDetail.value?.activityNodes) {
      console.warn('没有可用的审批节点数据');
      return;
    }
    return new Promise((resolve, reject) => {
      currentPromiseResolve = resolve;
      currentPromiseReject = reject;
      previewModalApi
        .setData({
          activityNodes: approvalDetail.value.activityNodes,
          simpleJson: approvalDetail.value.processDefinition.simpleModel,
          api: api || apiConfig,
        })
        .open();
    });
  };
  const useOperationButton = (additionalProps: any = {}) => {
    return defineComponent({
      name: 'WorkflowOperationButton',
      emits: ['success'],
      setup(_props, { attrs, emit }) {
        return () => {
          const defaultProps = {
            processDefinition: processDefinition.value,
            processInstance: processInstance.value,
            taskApi: apiConfig,
            ...additionalProps,
            ...attrs,
          };

          return h(OperationButton, {
            ref: OperationButtonRef,
            ...defaultProps,
            onSuccess: (data: any) => {
              emit('success', data);
            },
          });
        };
      },
    });
  };
  const useViewButton = (additionalProps: any = {}) => {
    return defineComponent({
      name: 'WorkflowViewButton',
      setup(_props, { attrs }) {
        return () => {
          const defaultProps = {
            taskApi: apiConfig,
            processInstanceId: config.value.processInstanceId,
            activityNodes: approvalDetail.value.activityNodes ?? [],
            ...additionalProps,
            ...attrs,
          };

          return h(ViewButton, {
            ...defaultProps,
          });
        };
      },
    });
  };

  return {
    initWorkflow,
    startWorkflow,
    isWorkflow,
    isProcessInstance,
    isRunningTask,
    isWorkflowLoading,
    processDefinition,
    processInstance,
    approvalDetail,
    WorkflowPreviewModal,
    previewModalApi,
    OperationButton,
    useOperationButton,
    ViewButton,
    useViewButton,
  };
}
